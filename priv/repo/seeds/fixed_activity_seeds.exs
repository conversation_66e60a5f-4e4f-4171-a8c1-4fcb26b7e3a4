# 修复后的活动系统种子数据
# 运行方式: mix run priv/repo/seeds/fixed_activity_seeds.exs

import Ash.Query

alias Teen.ActivitySystem.{
  FirstRechargeGift,
  LossRebateJar,
  ScratchCardActivity,
  WeeklyCard,
  BindingReward
}

IO.puts("🔧 开始修复活动系统种子数据...")

# 安全创建函数
safe_create = fn resource, data, unique_field ->
  case resource
       |> Ash.Query.filter(^[{unique_field, Map.get(data, unique_field)}])
       |> Ash.read_one() do
    {:ok, existing} when not is_nil(existing) ->
      {:exists, existing}
    {:ok, nil} ->
      case Ash.create(resource, data) do
        {:ok, created} -> {:ok, created}
        {:error, reason} -> {:error, reason}
      end
    {:error, reason} ->
      {:error, reason}
  end
end

# ==================== 1. 首充礼包 ====================
IO.puts("🎁 修复首充礼包配置...")

first_recharge_gifts = [
  %{
    title: "新手首充礼包",
    limit_days: 7,
    min_recharge_amount: Decimal.new("1000"),
    reward_coins: Decimal.new("1500"),
    reward_type: :coins,
    bonus_multiplier: Decimal.new("1.5"),
    time_limit_hours: 168,
    description: "新用户专享首充礼包，充值即可获得150%奖励",
    is_active: true,
    status: :enabled
  },
  %{
    title: "限时首充礼包",
    limit_days: 3,
    min_recharge_amount: Decimal.new("2000"),
    reward_coins: Decimal.new("4000"),
    reward_type: :coins,
    bonus_multiplier: Decimal.new("2.0"),
    time_limit_hours: 72,
    description: "限时特惠首充礼包，充值即可获得200%奖励",
    is_active: true,
    status: :enabled
  },
  %{
    title: "超值首充礼包",
    limit_days: 1,
    min_recharge_amount: Decimal.new("5000"),
    reward_coins: Decimal.new("15000"),
    reward_type: :coins,
    bonus_multiplier: Decimal.new("3.0"),
    time_limit_hours: 24,
    description: "超值首充礼包，充值即可获得300%奖励",
    is_active: true,
    status: :enabled
  }
]

Enum.each(first_recharge_gifts, fn gift_data ->
  case safe_create.(FirstRechargeGift, gift_data, :title) do
    {:ok, gift} -> IO.puts("✅ 首充礼包创建成功: #{gift.title}")
    {:exists, gift} -> IO.puts("ℹ️ 首充礼包已存在: #{gift.title}")
    {:error, reason} -> IO.puts("❌ 首充礼包创建失败: #{inspect(reason)}")
  end
end)

# ==================== 2. 亏损返利罐 ====================
IO.puts("🏺 修复亏损返利罐配置...")

loss_rebate_jars = [
  %{
    title: "新手保护罐",
    max_claims: 1,
    loss_threshold: Decimal.new("1000"),
    rebate_percentage: Decimal.new("10"),
    max_rebate: Decimal.new("500"),
    calculation_period: :daily,
    rebate_type: :coins,
    auto_distribute: false,
    is_active: true,
    status: :enabled
  },
  %{
    title: "标准返利罐",
    max_claims: 1,
    loss_threshold: Decimal.new("2000"),
    rebate_percentage: Decimal.new("12"),
    max_rebate: Decimal.new("1000"),
    calculation_period: :daily,
    rebate_type: :coins,
    auto_distribute: false,
    is_active: true,
    status: :enabled
  },
  %{
    title: "高级返利罐",
    max_claims: 2,
    loss_threshold: Decimal.new("5000"),
    rebate_percentage: Decimal.new("15"),
    max_rebate: Decimal.new("2000"),
    calculation_period: :daily,
    rebate_type: :coins,
    auto_distribute: true,
    is_active: true,
    status: :enabled
  }
]

Enum.each(loss_rebate_jars, fn jar_data ->
  case safe_create.(LossRebateJar, jar_data, :title) do
    {:ok, jar} -> IO.puts("✅ 亏损返利罐创建成功: #{jar.title}")
    {:exists, jar} -> IO.puts("ℹ️ 亏损返利罐已存在: #{jar.title}")
    {:error, reason} -> IO.puts("❌ 亏损返利罐创建失败: #{inspect(reason)}")
  end
end)

# ==================== 3. 刮刮卡活动 ====================
IO.puts("🎲 修复刮刮卡活动配置...")

scratch_card_activities = [
  %{
    activity_title: "30次刮卡活动",
    card_type: :option1,
    cost_amount: Decimal.new("100"),
    min_reward: Decimal.new("50"),
    max_reward: Decimal.new("5000"),
    claimable_count: 30,
    reward_probability: Decimal.new("80"),
    daily_limit: 5,
    is_active: true,
    status: :enabled
  },
  %{
    activity_title: "豪华刮卡活动",
    card_type: :option2,
    cost_amount: Decimal.new("500"),
    min_reward: Decimal.new("200"),
    max_reward: Decimal.new("20000"),
    claimable_count: 50,
    reward_probability: Decimal.new("85"),
    daily_limit: 10,
    is_active: true,
    status: :enabled
  }
]

Enum.each(scratch_card_activities, fn activity_data ->
  case safe_create.(ScratchCardActivity, activity_data, :activity_title) do
    {:ok, activity} -> IO.puts("✅ 刮刮卡活动创建成功: #{activity.activity_title}")
    {:exists, activity} -> IO.puts("ℹ️ 刮刮卡活动已存在: #{activity.activity_title}")
    {:error, reason} -> IO.puts("❌ 刮刮卡活动创建失败: #{inspect(reason)}")
  end
end)

# ==================== 4. 绑定奖励 ====================
IO.puts("📱 修复绑定奖励配置...")

binding_rewards = [
  %{
    title: "手机绑定奖励",
    binding_type: :phone,
    reward_amount: Decimal.new("500"),
    reward_type: :coins,
    one_time_only: true,
    verification_required: true,
    description: "绑定手机号码即可获得500金币奖励",
    is_active: true,
    status: :enabled
  },
  %{
    title: "邮箱绑定奖励",
    binding_type: :email,
    reward_amount: Decimal.new("300"),
    reward_type: :coins,
    one_time_only: true,
    verification_required: true,
    description: "绑定邮箱地址即可获得300金币奖励",
    is_active: true,
    status: :enabled
  },
  %{
    title: "银行卡绑定奖励",
    binding_type: :bank_card,
    reward_amount: Decimal.new("1000"),
    reward_type: :coins,
    one_time_only: true,
    verification_required: true,
    description: "绑定银行卡即可获得1000金币奖励",
    is_active: true,
    status: :enabled
  }
]

Enum.each(binding_rewards, fn reward_data ->
  case safe_create.(BindingReward, reward_data, :binding_type) do
    {:ok, reward} -> IO.puts("✅ 绑定奖励创建成功: #{reward.title}")
    {:exists, reward} -> IO.puts("ℹ️ 绑定奖励已存在: #{reward.title}")
    {:error, reason} -> IO.puts("❌ 绑定奖励创建失败: #{inspect(reason)}")
  end
end)

IO.puts("\n✅ 活动系统种子数据修复完成！")
IO.puts("🚀 所有活动配置已更新为最新的字段结构")
