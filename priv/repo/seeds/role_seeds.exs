# Seeds for Teen.SystemSettings.Role
# 角色系统种子数据

IO.puts("🔐 开始初始化角色系统...")

# 定义默认角色数据
roles = [
  %{
    name: "超级管理员",
    code: "super_admin",
    level: 2,
    status: 1,
    description: "系统最高权限管理员，拥有所有权限",
    permission_codes: ["*"],
    menu_permissions: ["*"],
    data_permissions: %{
      "all_access" => true,
      "can_manage_system" => true,
      "can_manage_users" => true,
      "can_manage_roles" => true
    }
  },
  %{
    name: "管理员",
    code: "admin",
    level: 1,
    status: 1,
    description: "普通管理员，可以管理游戏和用户",
    permission_codes: [
      "user:view",
      "user:create",
      "user:update",
      "game:view",
      "game:update",
      "report:view",
      "activity:manage"
    ],
    menu_permissions: [
      "dashboard",
      "users",
      "games",
      "reports",
      "activities"
    ],
    data_permissions: %{
      "can_manage_users" => true,
      "can_manage_games" => true,
      "can_view_reports" => true
    }
  },
  %{
    name: "运营人员",
    code: "operator",
    level: 0,
    status: 1,
    description: "运营人员，负责日常运营活动",
    permission_codes: [
      "activity:view",
      "activity:create",
      "activity:update",
      "user:view",
      "report:view"
    ],
    menu_permissions: [
      "dashboard",
      "activities",
      "reports"
    ],
    data_permissions: %{
      "can_manage_activities" => true,
      "can_view_reports" => true
    }
  },
  %{
    name: "客服",
    code: "customer_service",
    level: 0,
    status: 1,
    description: "客服人员，处理用户问题和反馈",
    permission_codes: [
      "user:view",
      "user:message",
      "feedback:view",
      "feedback:reply"
    ],
    menu_permissions: [
      "dashboard",
      "users",
      "feedback"
    ],
    data_permissions: %{
      "can_view_users" => true,
      "can_handle_feedback" => true
    }
  },
  %{
    name: "财务人员",
    code: "finance",
    level: 0,
    status: 1,
    description: "财务人员，管理充值提现等财务相关操作",
    permission_codes: [
      "payment:view",
      "payment:approve",
      "withdraw:view",
      "withdraw:approve",
      "report:financial"
    ],
    menu_permissions: [
      "dashboard",
      "payments",
      "withdrawals",
      "financial_reports"
    ],
    data_permissions: %{
      "can_manage_payments" => true,
      "can_approve_withdrawals" => true,
      "can_view_financial_reports" => true
    }
  },
  %{
    name: "数据分析师",
    code: "analyst",
    level: 0,
    status: 1,
    description: "数据分析师，查看和分析各类数据报表",
    permission_codes: [
      "report:view",
      "report:export",
      "analytics:view"
    ],
    menu_permissions: [
      "dashboard",
      "reports",
      "analytics"
    ],
    data_permissions: %{
      "can_view_all_reports" => true,
      "can_export_data" => true
    }
  },
  %{
    name: "测试角色",
    code: "test_role",
    level: 0,
    status: 0,
    description: "测试用角色，默认禁用",
    permission_codes: ["test:all"],
    menu_permissions: ["test"],
    data_permissions: %{
      "test_access" => true
    }
  }
]

# 创建角色
Enum.each(roles, fn role_data ->
  case Teen.SystemSettings.Role.get_by_code(role_data.code) do
    {:ok, _existing_role} ->
      IO.puts("⚠️  角色 '#{role_data.name}' (#{role_data.code}) 已存在，跳过创建")

    {:error, _} ->
      case Teen.SystemSettings.Role.create(role_data) do
        {:ok, role} ->
          IO.puts("✅ 成功创建角色: #{role.name} (#{role.code}) - 级别: #{role.level}")

        {:error, error} ->
          IO.puts("❌ 创建角色 '#{role_data.name}' 失败: #{inspect(error)}")
      end
  end
end)

# 验证角色创建结果
case Teen.SystemSettings.Role.read() do
  {:ok, roles} ->
    active_count = Enum.count(roles, fn r -> r.status == 1 end)
    IO.puts("📊 角色统计: 总计 #{length(roles)} 个角色，其中 #{active_count} 个已启用")

  {:error, _} ->
    IO.puts("❌ 无法读取角色列表")
end

IO.puts("✅ 角色系统初始化完成！")
