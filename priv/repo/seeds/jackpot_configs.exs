# 奖池配置种子数据
alias Teen.GameManagement.JackpotConfig
import Ash.Expr

# 安全创建函数
safe_create_jackpot = fn attrs ->
  case JackpotConfig
       |> Ash.Query.filter(expr(game_id == ^attrs.game_id and jackpot_id == ^attrs.jackpot_id))
       |> Ash.read_one() do
    {:ok, existing} when not is_nil(existing) ->
      IO.puts("ℹ️ 奖池配置已存在: #{attrs.name}")
      {:exists, existing}
    {:ok, nil} ->
      case Ash.create(JackpotConfig, attrs, authorize?: false) do
        {:ok, created} ->
          IO.puts("✅ 奖池配置创建成功: #{created.name}")
          {:ok, created}
        {:error, reason} ->
          IO.puts("❌ 奖池配置创建失败: #{attrs.name}, 原因: #{inspect(reason)}")
          {:error, reason}
      end
    {:error, reason} ->
      {:error, reason}
  end
end

# Slot777 奖池配置 (使用正确的数字ID)
safe_create_jackpot.(%{
  game_id: "40",
  jackpot_id: "main",
  name: "Slot777 主奖池",
  description: "Slot777 游戏的主要奖池，基于7符号数量触发",
  base_amount: 250000,
  current_balance: 250000,
  min_amount: 12500,
  max_amount: 5000000,
  contribution_rate: "0.02",
  weight: 1,
  priority: 1,
  status: :active,
  trigger_conditions: %{
    min_seven_count: 3,
    min_single_line_bet: 10
  }
})

# SlotCat 三奖池配置 (使用正确的数字ID)
safe_create_jackpot.(%{
  game_id: "42",
  jackpot_id: "left",
  name: "SlotCat 左奖池",
  description: "SlotCat 游戏的左侧奖池",
  base_amount: 625000,
  current_balance: 625000,
  min_amount: 31250,
  max_amount: 12500000,
  contribution_rate: "0.006",
  weight: 1,
  priority: 3,
  status: :active
})

safe_create_jackpot.(%{
  game_id: "42",
  jackpot_id: "right",
  name: "SlotCat 右奖池",
  description: "SlotCat 游戏的右侧奖池",
  base_amount: 1250000,
  current_balance: 1250000,
  min_amount: 62500,
  max_amount: 25000000,
  contribution_rate: "0.008",
  weight: 2,
  priority: 2,
  status: :active
})

safe_create_jackpot.(%{
  game_id: "42",
  jackpot_id: "center",
  name: "SlotCat 中央奖池",
  description: "SlotCat 游戏的中央奖池，最高优先级",
  base_amount: 2500000,
  current_balance: 2500000,
  min_amount: 125000,
  max_amount: 50000000,
  contribution_rate: "0.006",
  weight: 3,
  priority: 1,
  status: :active
})

# SlotNiu 单奖池配置 (使用正确的数字ID)
safe_create_jackpot.(%{
  game_id: "41",
  jackpot_id: "jackpot",
  name: "SlotNiu Jackpot奖池",
  description: "SlotNiu 游戏的唯一Jackpot奖池",
  base_amount: 500000,
  current_balance: 500000,
  min_amount: 25000,
  max_amount: 10000000,
  contribution_rate: "0.01",
  weight: 1,
  priority: 1,
  status: :active,
  reset_on_win: false
})

IO.puts("✅ 奖池配置种子数据创建完成！")
IO.puts("📊 已创建 5 个奖池配置：")
IO.puts("   - Slot777: 1个主奖池")
IO.puts("   - SlotCat: 3个奖池 (left, right, center)")
IO.puts("   - SlotNiu: 1个奖池 (jackpot)")
