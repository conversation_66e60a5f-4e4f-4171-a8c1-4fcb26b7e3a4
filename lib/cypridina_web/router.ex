defmodule CypridinaWeb.Router do
  use Cy<PERSON>ridinaWeb, :router

  use AshAuthentication.Phoenix.Router

  import AshAuthentication.Plug.Helpers
  import Oban.Web.Router
  import Backpex.Router
  # use ErrorTracker.Integrations.Plug
  # use ErrorTracker.Web, :router

  # 仅在开发环境导入AshAdmin.Router
  # if Application.compile_env(:cypridina, :dev_routes) do
  import AshAdmin.Router
  # end

  pipeline :mcp do
    plug AshAuthentication.Strategy.ApiKey.Plug,
      resource: Cypridina.Accounts.User,
      # Use `required?: false` to allow unauthenticated
      # users to connect, for example if some tools
      # are publicly accessible.
      required?: false,
      on_error: &__MODULE__.api_key_error/2
  end

  def api_key_error(conn, _opts) do
    conn
    |> Plug.Conn.put_status(401)
    |> Phoenix.Controller.json(%{error: "Unauthorized"})
    |> Plug.Conn.halt()
  end

  pipeline :browser do
    plug(:accepts, ["html"])
    plug(:fetch_session)
    plug(:fetch_live_flash)
    plug(:put_root_layout, html: {CypridinaWeb.Layouts, :root})
    plug(:protect_from_forgery)
    plug(:put_secure_browser_headers)
    plug(:load_from_session)
    plug(CypridinaWeb.Plugs.ReturnToPlug)
    plug Backpex.ThemeSelectorPlug
  end

  pipeline :api do
    plug(:accepts, ["json"])
    plug(:load_from_bearer)
    plug(:set_actor, :user)
    # plug AshAuthentication.Strategy.ApiKey.Plug, resource: Cypridina.Accounts.User
  end

  scope "/mcp" do
    pipe_through :mcp

    forward "/", AshAi.Mcp.Router,
      tools: [
        # list your tools here
        # :tool1,
        # :tool2,
        # For many tools, you will need to set the `protocol_version_statement` to the older version.
      ],
      protocol_version_statement: "2024-11-05",
      otp_app: :cypridina
  end

  scope "/" do
    pipe_through(:browser)

    case Cypridina.ProjectMode.current() do
      :teen -> get("/", CypridinaWeb.PageController, :redirect_to_admin_users)
      :race -> get("/", CypridinaWeb.PageController, :redirect_to_racing_game)
      :self -> get("/", CypridinaWeb.PageController, :home)
    end

    # 用户应用路由
    scope "/" do
      # 需要用户登录的路由
      ash_authentication_live_session :user_required,
        on_mount: {CypridinaWeb.LiveUserAuth, :user_required} do
        # Racing game routes - only available in race mode
        if Cypridina.ProjectMode.race?() do
          live("/app/racing_game", CypridinaWeb.RacingLive.Index, :index)
          # 管理面板路由 - 支持URL导航
          live("/admin_panel", RacingGame.Live.AdminPanelLive, :index)
          live("/admin_panel/profile", RacingGame.Live.AdminPanelLive, :profile)
          live("/admin_panel/users", RacingGame.Live.AdminPanelLive, :users)
          live("/admin_panel/subordinates", RacingGame.Live.AdminPanelLive, :subordinates)
          live("/admin_panel/stocks", RacingGame.Live.AdminPanelLive, :stocks)
          live("/admin_panel/bet_records", RacingGame.Live.AdminPanelLive, :bet_records)
        end

        # Teen game routes - only available in teen mode
        if Cypridina.ProjectMode.teen?() do
          live("/admin/games", Teen.Live.GameExpectation.GameManagementLive, :index)
        end

        # 聊天相关路由
        live("/chat", CypridinaWeb.ChatLive, :index)
        live("/chat/:session_id", CypridinaWeb.ChatLive, :session)

        # 聊天测试页面
        get("/chat_test", CypridinaWeb.ChatTestController, :index)
        post("/chat_test/create_session", CypridinaWeb.ChatTestController, :create_test_session)
        post("/chat_test/send_message", CypridinaWeb.ChatTestController, :send_test_message)

        # 支付相关页面
        get("/payment/success", PageController, :payment_success)

        # 活动测试页面
        get("/activity_test", CypridinaWeb.ActivityTestController, :index)
        post("/activity_test/protocol", CypridinaWeb.ActivityTestController, :test_protocol)
        post("/chat_test/upload_file", CypridinaWeb.ChatTestController, :upload_test_file)
        get("/chat_test/sessions/:user_id", CypridinaWeb.ChatTestController, :list_sessions)

        get(
          "/chat_test/messages/:session_id/:user_id",
          CypridinaWeb.ChatTestController,
          :list_messages
        )

        live("/admin/games/new", Teen.Live.GameExpectation.GameManagementLive, :new)
        live("/admin/games/:id/edit", Teen.Live.GameExpectation.GameManagementLive, :edit)
        live("/admin/games/:id/rooms", Teen.Live.GameExpectation.GameManagementLive, :rooms)

        live(
          "/admin/games/:id/rooms/new",
          Teen.Live.GameExpectation.GameManagementLive,
          :new_room
        )

        live(
          "/admin/games/:id/rooms/:room_id/edit",
          Teen.Live.GameExpectation.GameManagementLive,
          :edit_room
        )

        live(
          "/admin/games/:game_id/room_management",
          Teen.Live.GameExpectation.RoomManagementLive,
          :index
        )

        live(
          "/admin/games/:game_id/room_management/new",
          Teen.Live.GameExpectation.RoomManagementLive,
          :new
        )

        live(
          "/admin/games/:game_id/room_management/:room_id/edit",
          Teen.Live.GameExpectation.RoomManagementLive,
          :edit
        )

        live("/admin/inventory_control", Teen.Live.GameExpectation.InventoryControlLive, :index)

        live(
          "/admin/inventory_control/game/:game_id",
          Teen.Live.GameExpectation.GameWalletControlLive,
          :show
        )
      end

      # Race control routes - only available in race mode
      if Cypridina.ProjectMode.race?() do
        ash_authentication_live_session :race_control,
          on_mount: [{CypridinaWeb.LiveUserAuth, :admin_required}] do
          live("/race_control", RacingGame.Live.RaceControlLive, :index)
        end
      end

      # 可选用户登录的路由
      ash_authentication_live_session :user_optional,
        on_mount: {CypridinaWeb.LiveUserAuth, :user_optional} do
        # 可选登录的页面
      end

      ash_admin("/admin/ash_admin")
    end
  end

  # backpex管理后台 - 需要管理员权限
  scope "/admin", Teen do
    pipe_through :browser
    # Backpex 管理界面
    backpex_routes()
    # 统一的管理后台LiveView
    ash_authentication_live_session :admin_backend,
      on_mount: [{CypridinaWeb.LiveUserAuth, :admin_required}, Backpex.InitAssigns] do
      # 确保这里没有使用 :live_no_user 或 :no_user
      # 所有管理后台路由都应该使用 :admin_required

      # 仪表盘
      live "/dashboard", Live.Admin.DashboardLive, :index
      # 数据分析
      live "/analytics", Live.Admin.AnalyticsLive, :index
      # 主题管理
      live "/themes", Live.Admin.ThemeLive, :index
      # 图标测试页面
      live "/icon-test", Live.Admin.IconTestLive, :index
      # 所有图标展示页面
      live "/all-icons", Live.Admin.AllIconsLive, :index
      # 图标诊断页面
      live "/icon-diagnostic", Live.Admin.IconDiagnosticLive, :index
      # 图标冲突检测页面
      live "/icon-conflicts", Live.Admin.IconConflictCheckerLive, :index
      # 强制编译图标页面
      live "/icon-force-compile", Live.Admin.IconForceCompileLive, :index
      # 图标 测试页面
      live "/heroicons-test", Live.Admin.HeroiconsTestLive, :index
      # 用户概览
      live "/user-overview", Live.Admin.UserOverviewLive, :index
      # 用户管理 - 根据权限显示不同内容
      live_resources "/users", Live.UserLive
      # 封禁管理
      live_resources "/user-bans", Live.UserBanLive
      # 渠道管理
      live_resources "/channels", Live.ChannelLive
      # 用户设备管理
      live_resources "/user-devices", Live.UserDeviceLive
      # 支付配置管理
      live_resources "/payment-configs", Live.PaymentConfigLive
      # 支付网关管理
      live_resources "/payment-gateways", Live.PaymentGatewayLive
      # 提现配置管理
      live_resources "/withdrawal-configs", Live.WithdrawalConfigLive
      # 提现记录管理
      live_resources "/withdrawal-records", Live.WithdrawalRecordLive
      # 银行配置管理
      live_resources "/bank-configs", Live.BankConfigLive
      # 用户银行卡管理
      live_resources "/user-bank-cards", Live.UserBankCardLive
      # 支付订单管理
      live_resources "/payment-orders", Live.PaymentOrderLive

      # 商品系统管理
      live_resources "/products", Live.ProductLive
      live_resources "/product-templates", Live.ProductTemplateLive
      live_resources "/user-purchases", Live.UserPurchaseLive

      # 活动系统管理
      live_resources "/daily-game-tasks", Live.ActivitySystem.DailyGameTaskLive
      live_resources "/weekly-cards", Live.ActivitySystem.WeeklyCardLive
      live_resources "/seven-day-logins", Live.ActivitySystem.SevenDayLoginLive
      live_resources "/vip-gifts", Live.ActivitySystem.VipGiftLive
      live_resources "/recharge-tasks", Live.ActivitySystem.RechargeTaskLive
      live_resources "/recharge-wheels", Live.ActivitySystem.RechargeWheelLive
      live_resources "/scratch-card-activities", Live.ActivitySystem.ScratchCardActivityLive
      live_resources "/first-recharge-gifts", Live.ActivitySystem.FirstRechargeGiftLive
      live_resources "/loss-rebates", Live.ActivitySystem.LossRebateLive
      live_resources "/invite-cash-activities", Live.ActivitySystem.InviteCashActivityLive
      live_resources "/binding-rewards", Live.ActivitySystem.BindingRewardLive
      live_resources "/free-bonus-tasks", Live.ActivitySystem.FreeBonusTaskLive
      # 使用 Cinder 的新 CDKey 管理页面
      # live "/cdkeys", Live.ActivitySystem.CdkeyCinderLive, :index
      # 原有的 Backpex CDKey 管理页面（暂时保留）
      live_resources "/cdkeys", Live.ActivitySystem.CdkeyLive
      live_resources "/cdkeys-claim-records", Live.ActivitySystem.CdkeyClaimRecordLive

      # 活动系统记录管理
      live_resources "/user-activity-participations",
                     Live.ActivitySystem.UserActivityParticipationLive

      live_resources "/reward-claim-records", Live.ActivitySystem.RewardClaimRecordLive

      # 客服聊天系统
      live "/customer-service-chat", Live.CustomerServiceChatLive, :index

      # 通用聊天系统
      live "/chat", Live.ChatLive, :index

      # 奖池管理系统
      live "/jackpots", Live.Jackpot.JackpotManagementLive, :index
      live "/jackpots/new", Live.Jackpot.JackpotManagementLive, :new
      live "/jackpots/:id/edit", Live.Jackpot.JackpotManagementLive, :edit
      live "/chat/:session_id", Live.ChatLive, :show

      # 机器人管理系统
      live "/robots", Live.RobotManagementLive, :index

      # 幸运值管理系统
      live "/luck-management", Live.LuckManagementLive, :index

      # 游戏管理系统 - Backpex版本
      live_resources "/games-backpex", Live.GameExpectation.GameManagementBackpexLive
      live_resources "/rooms-backpex", Live.GameExpectation.RoomManagementBackpexLive

      # 新增的管理页面 - 移植自racing_game
      # 个人信息管理
      live "/profile", Live.ProfileLive, :index
      # 用户管理（管理员专用）
      live "/user-management", Live.UserManagementLive, :index
      # 下线管理（代理专用）
      live "/subordinate-management", Live.SubordinateManagementLive, :index
      # 系统日志查看
      live "/logs", Live.LogViewerLive, :index
      # 日志文件下载
      get "/logs/download", CypridinaWeb.LogController, :download
      # 系统配置管理
      live "/system-config", Live.SystemConfigLive, :index
    end
  end

  scope "/" do
    pipe_through(:api)

    # Racing game API routes - only available in race mode
    if Cypridina.ProjectMode.race?() do
      scope "/racing_game", CypridinaWeb do
        get("/getsettle", RacingGameController, :getsettle)
        get("/getinfo", RacingGameController, :getinfo)
        get("/getlatestdata", RacingGameController, :getlatestdata)
        post("/rank", RacingGameController, :rank)
      end
    end

    # 文件上传API
    scope "/api/upload", CypridinaWeb do
      post("/avatar", UploadController, :upload_avatar)
      post("/generate_url", UploadController, :generate_upload_url)
      get("/avatar/:user_id", UploadController, :get_avatar_url)
    end

    # 聊天API
    scope "/api/chat", CypridinaWeb do
      # 会话管理
      post("/sessions/private", ChatController, :create_private_session)
      post("/sessions/group", ChatController, :create_group_session)
      get("/sessions", ChatController, :list_sessions)
      get("/sessions/:id", ChatController, :get_chat_session)

      # 消息管理
      post("/sessions/:id/messages", ChatController, :send_message)
      get("/sessions/:id/messages", ChatController, :list_messages)
      post("/messages/:id/read", ChatController, :mark_message_read)
      post("/sessions/:id/read", ChatController, :mark_session_read)

      # 文件上传
      post("/sessions/:id/upload", ChatController, :upload_file)
      post("/sessions/:id/upload_url", ChatController, :generate_upload_url)
      post("/sessions/:id/confirm_upload", ChatController, :confirm_upload)

      # 参与者管理
      post("/sessions/:id/participants", ChatController, :add_participant)
      delete("/sessions/:id/participants/:user_id", ChatController, :remove_participant)

      # 未读消息统计
      get("/unread_count", ChatController, :get_unread_count)
    end

    # Teen 登录API
    scope "/api/teen", CypridinaWeb do
      post("/login", TeenApiController, :login)
      post("/send_sms_code", TeenApiController, :send_sms_code)
      get("/serverlist_v2", TeenApiController, :default)

      # Home API 接口
      get("/Home/GetMoneyExLogList", TeenApiController, :get_money_ex_log_list)
      get("/Home/GetPayOrderList", TeenApiController, :get_pay_order_list)
    end

    # 支付回调API
    scope "/api", CypridinaWeb do
      get("/payment/notify", PaymentController, :handle_notify)
      get "/withdrawal/notify", PaymentController, :handle_withdrawal_notify
    end

    # 模拟支付API (仅在开发/测试环境使用)
    scope "/api/mock-payment", CypridinaWeb do
      get("/test-tools", MockPaymentController, :test_tools)
      get("/:order_id", MockPaymentController, :show)
      get("/:order_id/success", MockPaymentController, :success)
      get("/:order_id/failure", MockPaymentController, :failure)
      post("/callback/:type/:order_id", MockPaymentController, :callback)

      # 在 router.ex 中添加
      get "/withdrawal/:order_id/success", MockPaymentController, :withdrawal_success
      get "/withdrawal/:order_id/failure", MockPaymentController, :withdrawal_failure
      post "/withdrawal/callback/:order_id", MockPaymentController, :withdrawal_callback
    end
  end

  # 认证相关
  scope "/", CypridinaWeb do
    pipe_through(:browser)

    auth_routes(AuthController, Cypridina.Accounts.User, path: "/auth")
    sign_out_route(AuthController)

    # 登录相关路由
    sign_in_route(
      register_path: "/register",
      reset_path: "/reset",
      auth_routes_prefix: "/auth",
      on_mount: [{CypridinaWeb.LiveUserAuth, :live_no_user}],
      overrides: [
        CypridinaWeb.AuthOverrides,
        AshAuthentication.Phoenix.Overrides.Default
      ]
    )

    # 重置密码路由
    reset_route(
      auth_routes_prefix: "/auth",
      overrides: [
        CypridinaWeb.AuthOverrides,
        AshAuthentication.Phoenix.Overrides.Default
      ]
    )

    # 用户确认路由
    confirm_route(Cypridina.Accounts.User, :confirm_new_user,
      auth_routes_prefix: "/auth",
      overrides: [CypridinaWeb.AuthOverrides, AshAuthentication.Phoenix.Overrides.Default]
    )
  end

  # Enable LiveDashboard and Swoosh mailbox preview in development
  if Application.compile_env(:cypridina, :dev_routes) do
    # If you want to use the LiveDashboard in production, you should put
    # it behind authentication and allow only admins to access it.
    # If your application does not have an admins-only section yet,
    # you can use Plug.BasicAuth to set up some basic authentication
    # as long as you are also using SSL (which you should anyway).
    import Phoenix.LiveDashboard.Router

    scope "/dev" do
      pipe_through(:browser)

      oban_dashboard("/oban")
      # error_tracker_dashboard("/errors")
      live_dashboard("/dashboard", metrics: CypridinaWeb.Telemetry)
      forward("/mailbox", Plug.Swoosh.MailboxPreview)
    end
  end
end
