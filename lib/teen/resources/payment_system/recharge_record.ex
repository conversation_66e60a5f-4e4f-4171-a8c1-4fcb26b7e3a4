defmodule Teen.PaymentSystem.RechargeRecord do
  @moduledoc """
  充值记录资源

  记录用户的充值信息，包括充值金额、支付方式、状态等
  配置了Ash Notifiers来自动发布充值事件
  """
  require Logger

  use Ash.Resource,
    otp_app: :cypridina,
    data_layer: AshPostgres.DataLayer,
    domain: Teen.PaymentSystem,
    extensions: [AshAdmin.Resource],
    notifiers: [Ash.Notifier.PubSub]

  admin do
    table_columns [:id, :user_id, :order_id, :amount, :status, :payment_method, :inserted_at]
  end

  postgres do
    table "recharge_records"
    repo Cypridina.Repo
  end

  code_interface do
    define :create
    define :read
    define :update
    define :destroy
    define :list_by_user
    define :get_by_order_id, action: :read, get_by: :order_id
    define :get_by_id, action: :read, get_by: :id

    define :complete_recharge
    define :fail_recharge
    define :create_recharge
    define :get_user_stats
    define :get_user_history
  end

  actions do
    defaults [:read, :update, :destroy]

    create :create do
      accept [:user_id, :order_id, :amount, :currency, :payment_method, :status]

      change fn changeset, _context ->
        changeset
        |> Ash.Changeset.change_attribute(:status, :pending)
      end
    end

    create :create_recharge do
      accept [:user_id, :amount, :payment_method, :currency]

      change fn changeset, _context ->
        # 生成订单号
        order_id = generate_order_id()

        changeset
        |> Ash.Changeset.change_attribute(:order_id, order_id)
        |> Ash.Changeset.change_attribute(:status, :pending)
      end
    end

    read :list_by_user do
      argument :user_id, :uuid, allow_nil?: false
      filter expr(user_id == ^arg(:user_id))
      prepare build(sort: [desc: :inserted_at])
    end

    read :get_user_history do
      argument :user_id, :uuid, allow_nil?: false
      argument :limit, :integer, default: 20
      argument :offset, :integer, default: 0
      argument :status, :atom

      filter expr(user_id == ^arg(:user_id))

      prepare fn query, context ->
        query = Ash.Query.limit(query, context.arguments.limit)
        query = Ash.Query.offset(query, context.arguments.offset)

        if context.arguments[:status] do
          Ash.Query.filter(query, expr(status == ^context.arguments.status))
        else
          query
        end
        |> Ash.Query.sort(inserted_at: :desc)
      end
    end

    read :get_user_stats do
      argument :user_id, :uuid, allow_nil?: false
      argument :start_date, :datetime
      argument :end_date, :datetime

      filter expr(user_id == ^arg(:user_id) and status == :completed)

      prepare fn query, context ->
        query =
          if context.arguments[:start_date] do
            Ash.Query.filter(query, expr(completed_at >= ^context.arguments.start_date))
          else
            query
          end

        if context.arguments[:end_date] do
          Ash.Query.filter(query, expr(completed_at <= ^context.arguments.end_date))
        else
          query
        end
      end
    end

    update :complete_recharge do
      accept [:external_order_id, :callback_data]
      # 确保原子性
      require_atomic? true

      # 使用内置的原子性友好操作，无需额外的 RechargeChange 模块
      change set_attribute(:status, :completed)
      change set_attribute(:completed_at, &DateTime.utc_now/0)
    end

    update :fail_recharge do
      accept [:error_message, :callback_data]
      # 确保原子性
      require_atomic? true

      change set_attribute(:status, :failed)
      change set_attribute(:failed_at, &DateTime.utc_now/0)
    end
  end

  # 配置PubSub通知，当充值记录变更时自动发布事件
  pub_sub do
    module CypridinaWeb.Endpoint
    prefix "recharge"

    # 发布充值完成事件
    publish :complete_recharge, ["recharge_completed", [:user_id]]

    # 发布充值失败事件
    publish :fail_recharge, ["recharge_failed", [:user_id]]

    # 发布所有创建和更新事件
    publish_all :create, [["recharge_created", :user_id]]
    publish_all :update, [["recharge_updated", :user_id]]
  end

  attributes do
    uuid_primary_key :id

    attribute :user_id, :uuid do
      allow_nil? false
      public? true
      description "用户ID"
    end

    attribute :order_id, :string do
      allow_nil? false
      public? true
      description "订单ID"
      constraints max_length: 100
    end

    attribute :external_order_id, :string do
      allow_nil? true
      public? true
      description "第三方订单ID"
      constraints max_length: 100
    end

    attribute :amount, :decimal do
      allow_nil? false
      public? true
      description "充值金额（分）"
      constraints min: Decimal.new("1")
    end

    attribute :currency, :string do
      allow_nil? false
      public? true
      description "货币类型"
      default "XAA"
      constraints max_length: 10
    end

    attribute :payment_method, :string do
      allow_nil? true
      public? true
      description "支付方式"
      constraints max_length: 50
    end

    attribute :status, :atom do
      allow_nil? false
      public? true
      description "充值状态"
      constraints one_of: [:pending, :processing, :completed, :failed, :cancelled]
      default :pending
    end

    attribute :callback_data, :map do
      allow_nil? true
      public? true
      description "回调数据"
      default %{}
    end

    attribute :error_message, :string do
      allow_nil? true
      public? true
      description "错误信息"
      constraints max_length: 500
    end

    attribute :completed_at, :utc_datetime do
      allow_nil? true
      public? true
      description "完成时间"
    end

    attribute :failed_at, :utc_datetime do
      allow_nil? true
      public? true
      description "失败时间"
    end

    timestamps()
  end

  relationships do
    belongs_to :user, Cypridina.Accounts.User do
      public? true
      source_attribute :user_id
      destination_attribute :id
    end
  end

  identities do
    identity :unique_order_id, [:order_id]
  end

  # 私有辅助函数
  defp generate_order_id do
    timestamp = System.system_time(:millisecond)
    random = :rand.uniform(9999) |> Integer.to_string() |> String.pad_leading(4, "0")
    "RC#{timestamp}#{random}"
  end

  defp add_coins_to_user(user_id, amount) do
    # 使用Ledger系统进行充值
    system_identifier = Cypridina.Ledger.AccountIdentifier.system(:main, :XAA)
    user_identifier = Cypridina.Ledger.AccountIdentifier.user(user_id, :XAA)

    # 确保金额是整数（分为单位）
    amount_integer =
      if is_struct(amount, Decimal) do
        Decimal.to_integer(amount)
      else
        amount
      end

    Logger.info("💰 [LEDGER] 准备转账: 从系统账户 -> 用户 #{user_id}, 金额: #{amount_integer}")

    case Cypridina.Ledger.transfer(
           system_identifier,
           user_identifier,
           amount_integer,
           transaction_type: :deposit,
           description: "用户充值"
         ) do
      {:ok, result} ->
        Logger.info("💰 [LEDGER] 转账成功: #{inspect(result)}")
        {:ok, result}

      {:error, reason} ->
        Logger.error("💰 [LEDGER] 转账失败: #{inspect(reason)}")
        {:error, reason}
    end
  end

  @doc """
  完成充值处理 - 使用 Reactor 工作流
  """
  def complete_recharge_order(order_id, callback_result) do
    Logger.info("完成充值订单 #{order_id}: #{inspect(callback_result)}")

    # 确保 callback_result 是 map 类型
    unless is_map(callback_result) do
      raise ArgumentError, "callback_result must be a map, got: #{inspect(callback_result)}"
    end

    # 处理 map 类型的 callback_result
    status = Map.get(callback_result, :status)

    case status do
      :success ->
        handle_completion_with_reactor(order_id, callback_result)

      :timeout ->
        handle_failure(order_id, "支付超时", callback_result)

      :rejected ->
        reject_reason = Map.get(callback_result, :reject_reason, "支付被拒绝")
        handle_failure(order_id, reject_reason, callback_result)

      _ ->
        handle_failure(order_id, "支付失败", callback_result)
    end
  end

  @doc """
  使用 Reactor 处理充值完成的完整业务流程
  """
  def complete_recharge_with_reactor(record_id, external_order_id, callback_data) do
    Logger.info("💰 [REACTOR] 启动充值完成工作流: record_id=#{record_id}")

    # 准备 Reactor 输入
    inputs = %{
      record_id: record_id,
      external_order_id: external_order_id,
      callback_data: callback_data
    }

    # 执行 Reactor 工作流
    case Reactor.run(Teen.PaymentSystem.CompleteRechargeReactorSimple, inputs) do
      {:ok, result} ->
        Logger.info("💰 [REACTOR] 工作流执行成功: #{inspect(result)}")
        {:ok, result.record}

      {:error, reason} ->
        Logger.error("💰 [REACTOR] 工作流执行失败: #{inspect(reason)}")
        {:error, reason}
    end
  end

  defp handle_completion_with_reactor(order_id, callback_result) do
    with {:ok, record} <- get_by_order_id(order_id) do
      complete_recharge_with_reactor(
        record.id,
        Map.get(callback_result, :gateway_order_id),
        callback_result
      )
    end
  end

  defp handle_failure(order_id, reason, callback_data) do
    with {:ok, record} <- get_by_order_id(order_id) do
      fail_recharge(record, %{
        error_message: reason,
        callback_data: callback_data
      })
    end
  end

  defp get_last_recharge_time([]), do: nil

  defp get_last_recharge_time(records) do
    records
    |> Enum.filter(& &1.completed_at)
    |> Enum.map(& &1.completed_at)
    |> Enum.max(DateTime, fn -> nil end)
  end
end
