defmodule Teen.PaymentSystem.BankConfig do
  @moduledoc """
  银行配置资源

  管理银行信息配置，包括银行名称、图标、状态等
  """

  use Ash.Resource,
    otp_app: :cypridina,
    domain: Teen.PaymentSystem,
    data_layer: AshPostgres.DataLayer,
    extensions: [AshAdmin.Resource]

  admin do
    table_columns [:id, :name, :bank_code, :status, :sort_order]
  end

  postgres do
    table "bank_configs"
    repo Cyprid<PERSON>.Repo
  end

  code_interface do
    define :create
    define :read
    define :update
    define :destroy
    define :list_active_banks
    define :get_by_code, action: :read, get_by: :bank_code
    define :enable
    define :disable
  end

  actions do
    defaults [:read, :update, :destroy]

    create :create do
      primary? true
      accept [
        :name,
        :bank_code,
        :icon_url,
        :status,
        :sort_order,
        :min_amount,
        :max_amount,
        :fee_rate,
        :config_data
      ]
    end

    read :list_active_banks do
      filter expr(status == 1)
    end

    update :enable do
      change set_attribute(:status, 1)
    end

    update :disable do
      change set_attribute(:status, 0)
    end
  end

  attributes do
    uuid_primary_key :id

    attribute :name, :string do
      allow_nil? false
      public? true
      description "银行名称"
      constraints max_length: 100
    end

    attribute :bank_code, :string do
      allow_nil? false
      public? true
      description "银行代码"
      constraints max_length: 20
    end

    attribute :icon_url, :string do
      allow_nil? true
      public? true
      description "银行图标URL"
      constraints max_length: 500
    end

    attribute :status, :integer do
      allow_nil? false
      public? true
      description "状态：0-禁用，1-启用"
      default 1
      constraints min: 0, max: 1
    end

    attribute :sort_order, :integer do
      allow_nil? false
      public? true
      description "排序"
      default 0
    end

    attribute :min_amount, :decimal do
      allow_nil? false
      public? true
      description "最小提现金额"
      default Decimal.new("100")
      constraints min: Decimal.new("0")
    end

    attribute :max_amount, :decimal do
      allow_nil? false
      public? true
      description "最大提现金额"
      default Decimal.new("100000")
      constraints min: Decimal.new("0")
    end

    attribute :fee_rate, :decimal do
      allow_nil? false
      public? true
      description "手续费率（%）"
      default Decimal.new("0")
      constraints min: Decimal.new("0"), max: Decimal.new("100")
    end

    attribute :config_data, :map do
      allow_nil? true
      public? true
      description "银行配置数据（JSON）"
    end

    timestamps()
  end

  identities do
    identity :unique_bank_code, [:bank_code]
  end
end
