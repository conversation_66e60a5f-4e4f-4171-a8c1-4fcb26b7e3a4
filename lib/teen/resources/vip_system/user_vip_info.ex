defmodule Teen.VipSystem.UserVipInfo do
  @moduledoc """
  用户VIP信息资源

  记录用户的VIP等级、经验值和相关统计
  """

  use Ash.Resource,
    domain: Teen.VipSystem,
    data_layer: AshPostgres.DataLayer,
    extensions: [AshAdmin.Resource]

  admin do
    read_actions [:read]
    create_actions [:create]
    update_actions [:update]
  end

  postgres do
    table "user_vip_infos"
    repo Cypridina.Repo
  end

  code_interface do
    define :read
    define :create
    define :update
    define :by_user_id, args: [:user_id]
    define :add_experience, args: [:user_id, :amount]
    define :claim_daily_bonus, args: [:user_id]
    define :reset_daily_stats
    define :get_or_create_vip_info, args: [:user_id]
    define :check_and_level_up
    define :update_withdrawal_record, args: [:user_id, :amount]
    define :update_recharge_record, args: [:user_id, :amount]
    define :check_withdrawal_permission, args: [:user_id, :amount]
  end

  actions do
    defaults [:read, :update]

    create :create do
      accept [
        :user_id,
        :vip_level,
        :experience,
        :total_experience,
        :level_up_time,
        :daily_bonus_claimed,
        :last_bonus_claim_time,
        :monthly_recharge,
        :monthly_withdrawal,
        :today_withdrawal_times,
        :today_withdrawal_amount,
        :statistics
      ]
    end

    read :by_user_id do
      argument :user_id, :string, allow_nil?: false
      filter expr(user_id == ^arg(:user_id))
    end

    update :add_experience do
      argument :user_id, :string, allow_nil?: false
      argument :amount, :decimal, allow_nil?: false

      filter expr(user_id == ^arg(:user_id))

      change fn changeset, _ ->
        current_exp = Ash.Changeset.get_attribute(changeset, :experience) || Decimal.new(0)
        total_exp = Ash.Changeset.get_attribute(changeset, :total_experience) || Decimal.new(0)

        changeset
        |> Ash.Changeset.change_attribute(
          :experience,
          Decimal.add(current_exp, changeset.arguments.amount)
        )
        |> Ash.Changeset.change_attribute(
          :total_experience,
          Decimal.add(total_exp, changeset.arguments.amount)
        )
      end
    end

    update :claim_daily_bonus do
      argument :user_id, :string, allow_nil?: false

      filter expr(user_id == ^arg(:user_id))

      change set_attribute(:daily_bonus_claimed, true)
      change set_attribute(:last_bonus_claim_time, &DateTime.utc_now/0)
    end

    update :reset_daily_stats do
      change set_attribute(:daily_bonus_claimed, false)
      change set_attribute(:today_withdrawal_times, 0)
      change set_attribute(:today_withdrawal_amount, 0)
    end

    create :get_or_create_vip_info do
      argument :user_id, :string, allow_nil?: false

      change fn changeset, _context ->
        user_id = Ash.Changeset.get_argument(changeset, :user_id)

        changeset
        |> Ash.Changeset.change_attribute(:user_id, user_id)
        |> Ash.Changeset.change_attribute(:vip_level, 0)
        |> Ash.Changeset.change_attribute(:experience, Decimal.new(0))
        |> Ash.Changeset.change_attribute(:total_experience, Decimal.new(0))
      end
    end

    update :check_and_level_up do
      require_atomic? false

      change fn changeset, _context ->
        vip_info = changeset.data
        current_exp = Ash.Changeset.get_attribute(changeset, :experience) || Decimal.new(0)
        current_level = Ash.Changeset.get_attribute(changeset, :vip_level) || 0

        case calculate_vip_level_from_experience(current_exp) do
          {:ok, new_level} when new_level > current_level ->
            changeset
            |> Ash.Changeset.change_attribute(:vip_level, new_level)
            |> Ash.Changeset.change_attribute(:level_up_time, DateTime.utc_now())

          _ ->
            changeset
        end
      end
    end

    update :update_withdrawal_record do
      argument :user_id, :string, allow_nil?: false
      argument :amount, :decimal, allow_nil?: false
      require_atomic? false

      filter expr(user_id == ^arg(:user_id))

      change fn changeset, _context ->
        amount = changeset.arguments.amount

        current_monthly =
          Ash.Changeset.get_attribute(changeset, :monthly_withdrawal) || Decimal.new(0)

        current_daily_times = Ash.Changeset.get_attribute(changeset, :today_withdrawal_times) || 0

        current_daily_amount =
          Ash.Changeset.get_attribute(changeset, :today_withdrawal_amount) || Decimal.new(0)

        changeset
        |> Ash.Changeset.change_attribute(
          :monthly_withdrawal,
          Decimal.add(current_monthly, amount)
        )
        |> Ash.Changeset.change_attribute(:today_withdrawal_times, current_daily_times + 1)
        |> Ash.Changeset.change_attribute(
          :today_withdrawal_amount,
          Decimal.add(current_daily_amount, amount)
        )
      end
    end

    update :update_recharge_record do
      argument :user_id, :string, allow_nil?: false
      argument :amount, :decimal, allow_nil?: false
      require_atomic? false

      filter expr(user_id == ^arg(:user_id))

      change fn changeset, _context ->
        amount = changeset.arguments.amount

        current_monthly =
          Ash.Changeset.get_attribute(changeset, :monthly_recharge) || Decimal.new(0)

        changeset
        |> Ash.Changeset.change_attribute(:monthly_recharge, Decimal.add(current_monthly, amount))
      end
    end

    read :check_withdrawal_permission do
      argument :user_id, :string, allow_nil?: false
      argument :amount, :decimal, allow_nil?: false

      filter expr(user_id == ^arg(:user_id))

      prepare fn query, context ->
        query
        |> Ash.Query.load([:vip_benefits, :withdrawal_permission])
      end
    end
  end

  attributes do
    integer_primary_key :id do
      # This tells Ash not to generate the ID, let the database do it
      generated? true
    end

    attribute :vip_level, :integer do
      allow_nil? false
      description "当前VIP等级"
      default 0
      constraints min: 0, max: 10
    end

    attribute :experience, :decimal do
      allow_nil? false
      description "当前经验值"
      default 0
    end

    attribute :total_experience, :decimal do
      allow_nil? false
      description "累计获得经验值"
      default 0
    end

    attribute :level_up_time, :utc_datetime do
      allow_nil? true
      description "最后升级时间"
    end

    attribute :daily_bonus_claimed, :boolean do
      allow_nil? false
      description "今日是否已领取VIP奖励"
      default false
    end

    attribute :last_bonus_claim_time, :utc_datetime do
      allow_nil? true
      description "最后领取奖励时间"
    end

    attribute :monthly_recharge, :decimal do
      allow_nil? false
      description "本月充值金额"
      default 0
    end

    attribute :monthly_withdrawal, :decimal do
      allow_nil? false
      description "本月提现金额"
      default 0
    end

    attribute :today_withdrawal_times, :integer do
      allow_nil? false
      description "今日提现次数"
      default 0
    end

    attribute :today_withdrawal_amount, :decimal do
      allow_nil? false
      description "今日提现金额"
      default 0
    end

    attribute :statistics, :map do
      allow_nil? true
      description "VIP统计数据"
      default %{}
    end

    create_timestamp :inserted_at
    update_timestamp :updated_at
  end

  relationships do
    belongs_to :user, Cypridina.Accounts.User do
      source_attribute :user_id
      destination_attribute :id
    end
  end

  calculations do
    calculate :next_level_experience, :decimal do
      calculation fn records, _ ->
        # This will be calculated in the VipService
        Enum.map(records, fn _ -> Decimal.new(0) end)
      end
    end

    calculate :experience_progress, :decimal do
      calculation fn records, _ ->
        Enum.map(records, fn record ->
          case get_next_level_requirement(record.vip_level) do
            {:ok, next_requirement} ->
              current_level_req = get_current_level_requirement(record.vip_level)
              level_diff = Decimal.sub(next_requirement, current_level_req)
              progress = Decimal.sub(record.total_experience, current_level_req)

              if Decimal.compare(level_diff, Decimal.new(0)) == :gt do
                Decimal.div(progress, level_diff) |> Decimal.mult(Decimal.new(100))
              else
                Decimal.new(100)
              end

            _ ->
              Decimal.new(100)
          end
        end)
      end
    end

    calculate :vip_benefits, :map do
      public? true
      description "VIP权益信息"

      calculation fn records, _ ->
        Enum.map(records, fn record ->
          case Teen.VipSystem.VipLevel.get_by_level(record.vip_level) do
            {:ok, level_config} ->
              %{
                daily_bonus: level_config.daily_bonus,
                recharge_bonus: level_config.recharge_bonus,
                exchange_rate_bonus: level_config.exchange_rate_bonus,
                withdrawal_limit: get_withdrawal_limit(record.vip_level),
                withdrawal_times: get_withdrawal_times(record.vip_level),
                privileges: level_config.privileges || []
              }

            _ ->
              get_default_benefits(record.vip_level)
          end
        end)
      end
    end

    calculate :withdrawal_permission, :map do
      public? true
      description "提现权限信息"
      argument :amount, :decimal

      calculation fn records, context ->
        amount = context.arguments[:amount] || Decimal.new(0)

        Enum.map(records, fn record ->
          benefits = get_vip_benefits_for_level(record.vip_level)
          daily_limit = benefits.withdrawal_limit
          daily_times_limit = benefits.withdrawal_times

          remaining_amount = Decimal.sub(daily_limit, record.today_withdrawal_amount)
          remaining_times = daily_times_limit - record.today_withdrawal_times

          can_withdraw = Decimal.compare(amount, remaining_amount) != :gt && remaining_times > 0

          %{
            can_withdraw: can_withdraw,
            remaining_amount: remaining_amount,
            remaining_times: remaining_times,
            daily_limit: daily_limit,
            daily_times_limit: daily_times_limit,
            reason:
              if(not can_withdraw,
                do: get_withdrawal_deny_reason(amount, remaining_amount, remaining_times),
                else: nil
              )
          }
        end)
      end
    end
  end

  identities do
    identity :unique_user, [:user_id]
  end

  # Helper functions for VIP operations

  defp calculate_vip_level_from_experience(experience) do
    # Experience requirements per level
    level_requirements = %{
      0 => Decimal.new(0),
      1 => Decimal.new(100),
      2 => Decimal.new(500),
      3 => Decimal.new(1500),
      4 => Decimal.new(3000),
      5 => Decimal.new(5000),
      6 => Decimal.new(10000),
      7 => Decimal.new(20000),
      8 => Decimal.new(50000),
      9 => Decimal.new(100_000),
      10 => Decimal.new(200_000)
    }

    new_level =
      level_requirements
      |> Enum.filter(fn {_level, req} -> Decimal.compare(experience, req) != :lt end)
      |> Enum.max_by(fn {level, _} -> level end, fn -> {0, Decimal.new(0)} end)
      |> elem(0)

    {:ok, new_level}
  end

  defp get_next_level_requirement(current_level) do
    level_requirements = %{
      0 => Decimal.new(100),
      1 => Decimal.new(500),
      2 => Decimal.new(1500),
      3 => Decimal.new(3000),
      4 => Decimal.new(5000),
      5 => Decimal.new(10000),
      6 => Decimal.new(20000),
      7 => Decimal.new(50000),
      8 => Decimal.new(100_000),
      9 => Decimal.new(200_000),
      10 => Decimal.new(999_999_999)
    }

    {:ok, Map.get(level_requirements, current_level, Decimal.new(999_999_999))}
  end

  defp get_current_level_requirement(level) do
    level_requirements = %{
      0 => Decimal.new(0),
      1 => Decimal.new(100),
      2 => Decimal.new(500),
      3 => Decimal.new(1500),
      4 => Decimal.new(3000),
      5 => Decimal.new(5000),
      6 => Decimal.new(10000),
      7 => Decimal.new(20000),
      8 => Decimal.new(50000),
      9 => Decimal.new(100_000),
      10 => Decimal.new(200_000)
    }

    Map.get(level_requirements, level, Decimal.new(0))
  end

  defp get_withdrawal_limit(vip_level) do
    limits = %{
      0 => Decimal.new(50000),
      1 => Decimal.new(100_000),
      2 => Decimal.new(200_000),
      3 => Decimal.new(500_000),
      4 => Decimal.new(1_000_000),
      5 => Decimal.new(2_000_000),
      6 => Decimal.new(5_000_000),
      7 => Decimal.new(10_000_000),
      8 => Decimal.new(20_000_000),
      9 => Decimal.new(50_000_000),
      10 => Decimal.new(100_000_000)
    }

    Map.get(limits, vip_level, Decimal.new(50000))
  end

  defp get_withdrawal_times(vip_level) do
    times = %{
      0 => 1,
      1 => 2,
      2 => 3,
      3 => 5,
      4 => 8,
      5 => 10,
      6 => 15,
      7 => 20,
      8 => 30,
      9 => 50,
      10 => 100
    }

    Map.get(times, vip_level, 1)
  end

  defp get_vip_benefits_for_level(vip_level) do
    %{
      withdrawal_limit: get_withdrawal_limit(vip_level),
      withdrawal_times: get_withdrawal_times(vip_level)
    }
  end

  defp get_default_benefits(vip_level) do
    %{
      daily_bonus: Decimal.mult(Decimal.new(10), Decimal.new(vip_level)),
      recharge_bonus: Decimal.mult(Decimal.new(0.01), Decimal.new(vip_level)),
      exchange_rate_bonus: Decimal.mult(Decimal.new(0.005), Decimal.new(vip_level)),
      withdrawal_limit: get_withdrawal_limit(vip_level),
      withdrawal_times: get_withdrawal_times(vip_level),
      privileges: []
    }
  end

  defp get_withdrawal_deny_reason(amount, remaining_amount, remaining_times) do
    cond do
      remaining_times <= 0 ->
        "今日提现次数已用完"

      Decimal.compare(amount, remaining_amount) == :gt ->
        "提现金额超过今日剩余限额"

      true ->
        "提现条件不满足"
    end
  end

  @doc """
  Get user VIP info with enriched data
  """
  def get_user_vip_info(user_id) do
    case by_user_id(user_id) do
      {:ok, [info | _]} ->
        # Load calculations
        enriched_info =
          Ash.load!(info, [:next_level_experience, :experience_progress, :vip_benefits],
            authorize?: false
          )

        {:ok, enriched_info}

      {:ok, []} ->
        # Create new VIP info
        case get_or_create_vip_info(user_id) do
          {:ok, info} ->
            enriched_info =
              Ash.load!(info, [:next_level_experience, :experience_progress, :vip_benefits],
                authorize?: false
              )

            {:ok, enriched_info}

          error ->
            error
        end

      {:error, reason} ->
        require Logger
        Logger.error("Failed to get user VIP info: #{inspect(reason)}")
        {:error, :fetch_error}
    end
  end

  @doc """
  Add experience and check for level up
  """
  def add_experience_with_level_up(user_id, amount, source \\ :other) do
    with {:ok, info} <- get_user_vip_info(user_id),
         {:ok, [updated_info | _]} <- __MODULE__.add_experience(user_id, amount),
         {:ok, final_info} <- check_and_level_up(updated_info) do
      # Publish experience change event
      Phoenix.PubSub.broadcast(
        Cypridina.PubSub,
        "vip:experience:#{user_id}",
        {:experience_added, %{user_id: user_id, amount: amount, source: source}}
      )

      # Check if level changed
      if final_info.vip_level > info.vip_level do
        Phoenix.PubSub.broadcast(
          Cypridina.PubSub,
          "vip:level:#{user_id}",
          {:level_up,
           %{user_id: user_id, old_level: info.vip_level, new_level: final_info.vip_level}}
        )
      end

      {:ok, final_info}
    end
  end

  # Experience source configuration
  @experience_sources %{
    # 充值1元 = 1经验
    recharge: 1.0,
    # 下注100元 = 1经验
    game_bet: 0.01,
    # 每日登录 = 10经验
    daily_login: 10,
    # 完成任务 = 20经验
    complete_task: 20,
    # 邀请好友 = 50经验
    invite_friend: 50
  }

  @doc """
  Add experience from specific source
  """
  def add_experience_from_source(user_id, source, value) do
    multiplier = Map.get(@experience_sources, source, 0)
    experience = calculate_experience(source, value, multiplier)

    if experience > 0 do
      add_experience_with_level_up(user_id, experience, source)
    else
      {:ok, nil}
    end
  end

  defp calculate_experience(source, value, multiplier) when source in [:recharge, :game_bet] do
    Decimal.mult(value, Decimal.new(multiplier)) |> Decimal.round(0)
  end

  defp calculate_experience(_source, _value, multiplier) do
    Decimal.new(multiplier)
  end

  @doc """
  Claim daily VIP bonus
  """
  def claim_daily_vip_bonus(user_id) do
    with {:ok, info} <- get_user_vip_info(user_id),
         {:ok, can_claim} <- check_daily_bonus_claimable(info),
         true <- can_claim,
         {:ok, updated_info} <- __MODULE__.claim_daily_bonus(user_id) do
      bonus_amount = info.vip_benefits.daily_bonus

      # Distribute bonus through ledger
      distribute_daily_bonus(user_id, bonus_amount)

      {:ok, %{claimed: true, amount: bonus_amount, vip_level: info.vip_level}}
    else
      false ->
        {:error, "今日奖励已领取"}

      {:error, reason} ->
        {:error, reason}
    end
  end

  defp check_daily_bonus_claimable(info) do
    if info.daily_bonus_claimed do
      {:ok, false}
    else
      {:ok, true}
    end
  end

  defp distribute_daily_bonus(user_id, amount) do
    # Use ledger system to distribute bonus
    from_account = "system:XAA:vip_rewards"
    to_account = "user:XAA:#{user_id}"

    Cypridina.Ledger.game_win(from_account, to_account, Decimal.to_integer(amount), "VIP每日奖励")
  end
end
