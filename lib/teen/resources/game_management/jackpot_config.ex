defmodule Teen.GameManagement.JackpotConfig do
  @moduledoc """
  奖池配置资源

  管理游戏奖池的配置信息，包括：
  - 奖池底金设置
  - 贡献率配置
  - 触发条件管理
  - 奖池状态监控
  """

  use Ash.Resource,
    otp_app: :cypridina,
    data_layer: AshPostgres.DataLayer,
    domain: Teen.GameManagement,
    extensions: [AshAdmin.Resource]

  admin do
    table_columns [
      :id,
      :game_id,
      :jackpot_id,
      :base_amount,
      :current_balance,
      :contribution_rate,
      :status,
      :updated_at
    ]
  end

  postgres do
    table "jackpot_configs"
    repo Cypridina.Repo
  end

  code_interface do
    define :create
    define :read
    define :update
    define :destroy
    define :list_by_game
    define :get_by_game_and_jackpot
    define :list_active_configs
  end

  actions do
    defaults [:read, :destroy]

    create :create do
      accept [
        :game_id,
        :jackpot_id,
        :name,
        :description,
        :base_amount,
        :current_balance,
        :min_amount,
        :max_amount,
        :contribution_rate,
        :weight,
        :priority,
        :status,
        :trigger_conditions,
        :reset_on_win,
        :auto_reset_threshold
      ]
    end

    update :update do
      accept [
        :name,
        :description,
        :base_amount,
        :current_balance,
        :min_amount,
        :max_amount,
        :contribution_rate,
        :weight,
        :priority,
        :status,
        :trigger_conditions,
        :reset_on_win,
        :auto_reset_threshold
      ]
    end

    # 按游戏ID查询
    read :list_by_game do
      argument :game_id, :string, allow_nil?: false
      filter expr(game_id == ^arg(:game_id))
    end

    # 按游戏ID和奖池ID查询
    read :get_by_game_and_jackpot do
      argument :game_id, :string, allow_nil?: false
      argument :jackpot_id, :string, allow_nil?: false
      filter expr(game_id == ^arg(:game_id) and jackpot_id == ^arg(:jackpot_id))
      get? true
    end

    # 查询启用的配置
    read :list_active_configs do
      filter expr(status == :active)
    end

    # 初始化奖池
    update :initialize_jackpot do
      argument :amount, :integer, allow_nil?: false
      require_atomic? false

      change fn changeset, _context ->
        amount = Ash.Changeset.get_argument(changeset, :amount)

        changeset
        |> Ash.Changeset.change_attribute(:current_balance, amount)
        |> Ash.Changeset.change_attribute(:last_initialized_at, DateTime.utc_now())
      end
    end

    # 重置奖池
    update :reset_jackpot do
      require_atomic? false

      change fn changeset, _context ->
        base_amount = Ash.Changeset.get_attribute(changeset, :base_amount)

        changeset
        |> Ash.Changeset.change_attribute(:current_balance, base_amount)
        |> Ash.Changeset.change_attribute(:last_reset_at, DateTime.utc_now())
      end
    end

    # 更新余额
    update :update_balance do
      argument :new_balance, :integer, allow_nil?: false
      require_atomic? false

      change fn changeset, _context ->
        new_balance = Ash.Changeset.get_argument(changeset, :new_balance)

        changeset
        |> Ash.Changeset.change_attribute(:current_balance, new_balance)
        |> Ash.Changeset.change_attribute(:last_updated_at, DateTime.utc_now())
      end
    end

    # 更新贡献率
    update :update_contribution_rate do
      accept [:contribution_rate]
      require_atomic? false

      change set_attribute(:last_updated_at, &DateTime.utc_now/0)
    end
  end

  resource do
    description "奖池配置资源，管理游戏奖池的配置信息"

    # 默认排序
    defaults do
      sort [asc: :priority, desc: :updated_at]
    end
  end

  validations do
    validate compare(:min_amount, less_than_or_equal_to: :max_amount),
      message: "最小金额不能大于最大金额"

    validate compare(:base_amount, greater_than_or_equal_to: :min_amount),
      message: "底金不能小于最小金额"

    validate compare(:base_amount, less_than_or_equal_to: :max_amount),
      message: "底金不能大于最大金额"
  end

  attributes do
    uuid_primary_key :id

    attribute :game_id, :string do
      allow_nil? false
      public? true
      description "游戏ID"
      constraints max_length: 50
    end

    attribute :jackpot_id, :string do
      allow_nil? false
      public? true
      description "奖池ID"
      constraints max_length: 50
    end

    attribute :name, :string do
      allow_nil? false
      public? true
      description "奖池名称"
      constraints max_length: 100
    end

    attribute :description, :string do
      allow_nil? true
      public? true
      description "奖池描述"
      constraints max_length: 500
    end

    attribute :base_amount, :integer do
      allow_nil? false
      public? true
      description "底金金额"
      default 0
      constraints min: 0
    end

    attribute :current_balance, :integer do
      allow_nil? false
      public? true
      description "当前余额"
      default 0
      constraints min: 0
    end

    attribute :min_amount, :integer do
      allow_nil? false
      public? true
      description "最小金额"
      default 0
      constraints min: 0
    end

    attribute :max_amount, :integer do
      allow_nil? false
      public? true
      description "最大金额"
      default 100_000_000
      constraints min: 0
    end

    attribute :contribution_rate, :decimal do
      allow_nil? false
      public? true
      description "贡献率（0-1之间的小数）"
      default "0.02"
      constraints min: "0", max: "1"
    end

    attribute :weight, :integer do
      allow_nil? false
      public? true
      description "权重（用于多奖池分配）"
      default 1
      constraints min: 1
    end

    attribute :priority, :integer do
      allow_nil? false
      public? true
      description "优先级（数字越小优先级越高）"
      default 1
      constraints min: 1
    end

    attribute :status, :atom do
      allow_nil? false
      public? true
      description "状态"
      default :active
      constraints one_of: [:active, :inactive, :maintenance]
    end

    attribute :trigger_conditions, :map do
      allow_nil? true
      public? true
      description "触发条件配置"
    end

    attribute :reset_on_win, :boolean do
      allow_nil? false
      public? true
      description "中奖后是否重置"
      default false
    end

    attribute :auto_reset_threshold, :integer do
      allow_nil? true
      public? true
      description "自动重置阈值"
      constraints min: 0
    end

    attribute :last_initialized_at, :utc_datetime do
      allow_nil? true
      public? true
      description "最后初始化时间"
    end

    attribute :last_reset_at, :utc_datetime do
      allow_nil? true
      public? true
      description "最后重置时间"
    end

    attribute :last_updated_at, :utc_datetime do
      allow_nil? true
      public? true
      description "最后更新时间"
    end

    timestamps()
  end

  calculations do
    calculate :is_healthy, :boolean do
      description "奖池是否健康（余额在正常范围内）"
      calculation expr(current_balance >= min_amount and current_balance <= max_amount)
    end

    calculate :balance_percentage, :decimal do
      description "当前余额相对于最大金额的百分比"

      calculation expr(
                    if(
                      max_amount > 0,
                      current_balance / max_amount * 100,
                      0
                    )
                  )
    end

    calculate :needs_reset, :boolean do
      description "是否需要重置"

      calculation expr(
                    not is_nil(auto_reset_threshold) and current_balance >= auto_reset_threshold
                  )
    end
  end

  identities do
    identity :unique_game_jackpot, [:game_id, :jackpot_id]
  end
end
