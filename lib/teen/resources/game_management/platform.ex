defmodule Teen.GameManagement.Platform do
  @moduledoc """
  平台配置资源

  管理游戏平台的基本配置信息，包括平台名称、公告、下载地址等
  """

  use Ash.Resource,
    otp_app: :cypridina,
    data_layer: AshPostgres.DataLayer,
    domain: Teen.GameManagement,
    extensions: [AshAdmin.Resource]

  admin do
    table_columns [
      :id,
      :platform_number,
      :platform_name,
      :agent_recharge_switch,
      :status,
      :inserted_at
    ]
  end

  postgres do
    table "platforms"
    repo Cypridina.Repo
  end

  code_interface do
    define :create
    define :read
    define :update
    define :destroy
    define :list_active_platforms
    define :get_by_platform_number
  end

  actions do
    defaults [:read, :destroy, create: :*, update: :*]

    read :list_active_platforms do
      filter expr(status == 1)
    end

    read :get_by_platform_number do
      argument :platform_number, :string, allow_nil?: false
      filter expr(platform_number == ^arg(:platform_number))
    end

    update :enable_platform do
      change set_attribute(:status, 1)
    end

    update :disable_platform do
      change set_attribute(:status, 0)
    end

    update :toggle_agent_recharge do
      change fn changeset, _context ->
        current_switch = Ash.Changeset.get_data(changeset).agent_recharge_switch
        new_switch = if current_switch == 1, do: 0, else: 1
        Ash.Changeset.change_attribute(changeset, :agent_recharge_switch, new_switch)
      end
    end
  end

  resource do
    description "平台配置资源，管理游戏平台的基本配置信息"

    # 默认排序
    defaults do
      sort [asc: :sort_order, desc: :inserted_at]
    end
  end

  attributes do
    uuid_primary_key :id

    attribute :platform_number, :string do
      allow_nil? false
      public? true
      description "平台编号"
      constraints max_length: 50
    end

    attribute :platform_name, :string do
      allow_nil? false
      public? true
      description "平台名称"
      constraints max_length: 100
    end

    attribute :platform_announcement, :string do
      allow_nil? true
      public? true
      description "平台公告"
      constraints max_length: 2000
    end

    attribute :agent_recharge_switch, :integer do
      allow_nil? false
      public? true
      description "代理充值开关：0-关闭，1-开启"
      default 0
      constraints min: 0, max: 1
    end

    attribute :agent_platform_number, :string do
      allow_nil? true
      public? true
      description "代理平台编号"
      constraints max_length: 50
    end

    attribute :login_platform_number, :string do
      allow_nil? true
      public? true
      description "登录平台编号"
      constraints max_length: 50
    end

    attribute :show_qr_code, :integer do
      allow_nil? false
      public? true
      description "是否显示二维码：0-隐藏，1-展示"
      default 1
      constraints min: 0, max: 1
    end

    attribute :default_download_url, :string do
      allow_nil? true
      public? true
      description "默认下载地址"
      constraints max_length: 500
    end

    attribute :ios_download_url, :string do
      allow_nil? true
      public? true
      description "iOS下载地址"
      constraints max_length: 500
    end

    attribute :android_download_url, :string do
      allow_nil? true
      public? true
      description "安卓下载地址"
      constraints max_length: 500
    end

    attribute :status, :integer do
      allow_nil? false
      public? true
      description "平台状态：0-禁用，1-启用"
      default 1
      constraints min: 0, max: 1
    end

    attribute :sort_order, :integer do
      allow_nil? false
      public? true
      description "排序"
      default 0
    end

    attribute :config_data, :map do
      allow_nil? true
      public? true
      description "平台配置数据（JSON）"
    end

    timestamps()
  end

  identities do
    identity :unique_platform_number, [:platform_number]
    identity :unique_platform_name, [:platform_name]
  end
end
