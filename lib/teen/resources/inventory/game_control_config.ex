defmodule Teen.Resources.Inventory.GameControlConfig do
  @moduledoc """
  游戏控制配置资源

  管理各种游戏的控制配置：
  - 基础库存设置
  - 控制权重配置
  - 暗税比例设置
  - 游戏类型分类
  """

  use Ash.Resource,
    domain: Teen.GameManagement,
    data_layer: AshPostgres.DataLayer

  postgres do
    table "game_control_configs"
    repo <PERSON><PERSON><PERSON><PERSON>.Repo
  end

  code_interface do
    define :create, action: :create
    define :update, action: :update
    define :read_all, action: :read

    define :get_by_game_id_and_type,
      action: :get_by_game_id_and_type,
      args: [:game_id, :game_type]

    define :list_by_game_type, action: :list_by_game_type, args: [:game_type]
    define :list_active_configs, action: :list_active_configs
  end

  actions do
    defaults [:read]

    create :create do
      primary? true

      accept [
        :game_id,
        :game_type,
        :game_name,
        :base_inventory,
        :control_weight,
        :dark_tax_rate,
        :winner_tax_rate,
        :collect_line_max,
        :collect_line_min,
        :collect_line_ratio,
        :pre_collect_line_ratio,
        :release_line_max,
        :release_line_min,
        :release_line_ratio,
        :pre_release_line_ratio,
        :long_weight,
        :hu_weight,
        :he_weight,
        :is_active
      ]
    end

    update :update do
      primary? true

      accept [
        :game_name,
        :base_inventory,
        :control_weight,
        :dark_tax_rate,
        :winner_tax_rate,
        :collect_line_max,
        :collect_line_min,
        :collect_line_ratio,
        :pre_collect_line_ratio,
        :release_line_max,
        :release_line_min,
        :release_line_ratio,
        :pre_release_line_ratio,
        :long_weight,
        :hu_weight,
        :he_weight,
        :is_active
      ]
    end

    read :get_by_game_id_and_type do
      argument :game_id, :integer, allow_nil?: false
      argument :game_type, :integer, allow_nil?: false

      filter expr(game_id == ^arg(:game_id) and game_type == ^arg(:game_type))
      get? true
    end

    read :list_by_game_type do
      argument :game_type, :integer, allow_nil?: false
      filter expr(game_type == ^arg(:game_type))
    end

    read :list_active_configs do
      filter expr(is_active == true)
    end
  end

  resource do
    description "游戏控制配置资源，管理各种游戏的控制配置"

    # 默认排序
    defaults do
      sort [asc: :game_type, asc: :game_id]
    end
  end

  validations do
    validate numericality(:control_weight,
               greater_than_or_equal_to: 1,
               less_than_or_equal_to: 1000
             ) do
      message "控制权重必须在1-1000之间"
    end

    validate numericality(:dark_tax_rate,
               greater_than_or_equal_to: -1000,
               less_than_or_equal_to: 1000
             ) do
      message "暗税比例必须在-1000到1000之间（-1000=-100%, 1000=100%）"
    end

    validate numericality(:winner_tax_rate,
               greater_than_or_equal_to: Decimal.new("0"),
               less_than_or_equal_to: Decimal.new("1.0")
             ) do
      message "明税比例必须在0-100%之间（例如0.05表示5%）"
    end

    validate numericality(:game_type, greater_than_or_equal_to: 1, less_than_or_equal_to: 3) do
      message "游戏类型必须是1(单人)、2(多人)或3(百人场)"
    end

    # 收分线配置验证
    validate numericality(:collect_line_max, greater_than: 0, less_than: 10_000_000) do
      message "收分线浮动最大值必须在0-1000万之间"
    end

    validate numericality(:collect_line_min, greater_than: 0, less_than: 10_000_000) do
      message "收分线浮动最小值必须在0-1000万之间"
    end

    validate numericality(:collect_line_ratio,
               greater_than: Decimal.new("0"),
               less_than: Decimal.new("1.0")
             ) do
      message "收分线浮动比例必须在0-100%之间"
    end

    validate numericality(:pre_collect_line_ratio,
               greater_than: Decimal.new("0"),
               less_than_or_equal_to: Decimal.new("1.0")
             ) do
      message "前置收分线比例必须在0-100%之间"
    end

    # 放分线配置验证
    validate numericality(:release_line_max, greater_than: 0, less_than: 10_000_000) do
      message "放分线浮动最大值必须在0-1000万之间"
    end

    validate numericality(:release_line_min, greater_than: 0, less_than: 10_000_000) do
      message "放分线浮动最小值必须在0-1000万之间"
    end

    validate numericality(:release_line_ratio,
               greater_than: Decimal.new("0"),
               less_than: Decimal.new("1.0")
             ) do
      message "放分线浮动比例必须在0-100%之间"
    end

    validate numericality(:pre_release_line_ratio,
               greater_than: Decimal.new("0"),
               less_than_or_equal_to: Decimal.new("1.0")
             ) do
      message "前置放分线比例必须在0-100%之间"
    end

    # 龙虎和权重验证
    validate numericality(:long_weight, greater_than_or_equal_to: 0, less_than_or_equal_to: 100) do
      message "龙的权重比例必须在0-100%之间"
    end

    validate numericality(:hu_weight, greater_than_or_equal_to: 0, less_than_or_equal_to: 100) do
      message "虎的权重比例必须在0-100%之间"
    end

    validate numericality(:he_weight, greater_than_or_equal_to: 0, less_than_or_equal_to: 100) do
      message "和的权重比例必须在0-100%之间"
    end

    # 注意：龙虎和权重总和应为100%，请手动确保 long_weight + hu_weight + he_weight = 100
  end

  attributes do
    uuid_primary_key :id

    attribute :game_id, :integer do
      allow_nil? false
      description "游戏ID"
    end

    attribute :game_type, :integer do
      allow_nil? false
      description "游戏类型：1=单人，2=多人，3=百人场"
    end

    attribute :game_name, :string do
      allow_nil? false
      description "游戏名称"
    end

    attribute :base_inventory, :decimal do
      allow_nil? false
      default 1_000_000
      description "基础库存"
    end

    attribute :control_weight, :integer do
      allow_nil? false
      default 500
      description "控制权重：1-1000"
    end

    attribute :dark_tax_rate, :integer do
      allow_nil? false
      default -50
      description "暗税比例：10=1%, 100=10%, -10=-1%（负值中心线上升）"
    end

    attribute :winner_tax_rate, :decimal do
      allow_nil? false
      default 0.05
      description "明税比例：对赢家抽水的百分比，例如0.05表示5%"
    end

    # 收分线浮动配置
    attribute :collect_line_max, :integer do
      allow_nil? false
      default 30000
      description "收分线浮动最大值"
    end

    attribute :collect_line_min, :integer do
      allow_nil? false
      default 5000
      description "收分线浮动最小值"
    end

    attribute :collect_line_ratio, :decimal do
      allow_nil? false
      default 0.2
      description "收分线浮动比例"
    end

    attribute :pre_collect_line_ratio, :decimal do
      allow_nil? false
      default 0.7
      description "前置收分线比例"
    end

    # 放分线浮动配置
    attribute :release_line_max, :integer do
      allow_nil? false
      default 30000
      description "放分线浮动最大值"
    end

    attribute :release_line_min, :integer do
      allow_nil? false
      default 5000
      description "放分线浮动最小值"
    end

    attribute :release_line_ratio, :decimal do
      allow_nil? false
      default 0.2
      description "放分线浮动比例"
    end

    attribute :pre_release_line_ratio, :decimal do
      allow_nil? false
      default 0.7
      description "前置放分线比例"
    end

    # 龙虎和权重配置（针对龙虎斗游戏）
    attribute :long_weight, :integer do
      allow_nil? false
      default 33
      description "龙的权重比例（%）"
    end

    attribute :hu_weight, :integer do
      allow_nil? false
      default 33
      description "虎的权重比例（%）"
    end

    attribute :he_weight, :integer do
      allow_nil? false
      default 34
      description "和的权重比例（%）"
    end

    attribute :is_active, :boolean do
      allow_nil? false
      default true
      description "是否启用"
    end

    timestamps()
  end

  identities do
    identity :unique_game_and_type, [:game_id, :game_type]
  end
end
