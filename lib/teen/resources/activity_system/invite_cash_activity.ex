defmodule Teen.ActivitySystem.InviteCashActivity do
  @moduledoc """
  拼多多邀请提现（Free Cash）资源

  管理邀请活动配置
  包括奖励总金额、初始奖励范围等
  """

  use Ash.Resource,
    otp_app: :cypridina,
    data_layer: AshPostgres.DataLayer,
    domain: Teen.ActivitySystem,
    extensions: [AshAdmin.Resource]

  admin do
    table_columns [:id, :title, :total_reward, :initial_min, :initial_max, :status, :updated_at]
  end

  postgres do
    table "invite_cash_activities"
    repo Cypridina.Repo
  end

  code_interface do
    define :create
    define :read
    define :update
    define :destroy
    define :list_active_activities
    define :enable_activity
    define :disable_activity
    define :check_invite_conditions
    define :check_cash_claimed
    define :claim_cash
  end

  actions do
    defaults [:read, :update, :destroy]

    create :create do
      accept [:title, :total_reward, :initial_min, :initial_max, :status]

      change fn changeset, _context ->
        changeset
        |> Ash.Changeset.change_attribute(:status, :enabled)
      end
    end

    read :list_active_activities do
      filter expr(status == :enabled)
    end

    update :enable_activity do
      require_atomic? false
      change set_attribute(:status, :enabled)
    end

    update :disable_activity do
      require_atomic? false
      change set_attribute(:status, :disabled)
    end

    read :check_invite_conditions do
      argument :user_id, :string, allow_nil?: false

      prepare fn query, context ->
        query
        |> Ash.Query.load([:invite_conditions_met])
      end
    end

    read :check_cash_claimed do
      argument :user_id, :string, allow_nil?: false

      prepare fn query, context ->
        query
        |> Ash.Query.load([:cash_claimed])
      end
    end

    update :claim_cash do
      argument :user_id, :string, allow_nil?: false
      require_atomic? false

      validate fn changeset, _context ->
        user_id = Ash.Changeset.get_argument(changeset, :user_id)
        activity = changeset.data

        # Check if invite conditions are met
        case check_invite_conditions_met(user_id) do
          false ->
            {:error, field: :user_id, message: "邀请条件尚未满足"}

          true ->
            # Check if already claimed
            case check_cash_reward_claimed(user_id) do
              true -> {:error, field: :user_id, message: "免费提现已领取"}
              false -> :ok
            end
        end
      end

      change fn changeset, context ->
        user_id = Ash.Changeset.get_argument(changeset, :user_id)
        activity = changeset.data

        # Distribute reward
        reward_amount = Decimal.to_integer(activity.total_reward)

        case distribute_cash_reward(user_id, reward_amount, "免费提现奖励") do
          {:ok, _} ->
            # Record claim
            record_cash_claim(user_id)
            changeset

          {:error, reason} ->
            Ash.Changeset.add_error(changeset,
              field: :reward,
              message: "奖励发放失败: #{inspect(reason)}"
            )
        end
      end
    end
  end

  attributes do
    uuid_primary_key :id

    attribute :title, :string do
      allow_nil? false
      public? true
      description "标题"
      constraints max_length: 100
    end

    attribute :total_reward, :decimal do
      allow_nil? false
      public? true
      description "奖励总金额（分）"
      constraints min: Decimal.new("0")
    end

    attribute :initial_min, :decimal do
      allow_nil? false
      public? true
      description "初始最小值（分）"
      constraints min: Decimal.new("0")
    end

    attribute :initial_max, :decimal do
      allow_nil? false
      public? true
      description "初始最大值（分）"
      constraints min: Decimal.new("0")
    end

    attribute :status, :atom do
      allow_nil? false
      public? true
      description "状态"
      constraints one_of: [:enabled, :disabled]
      default :enabled
    end

    timestamps()
  end

  relationships do
    has_many :reward_configs, Teen.ActivitySystem.InviteRewardConfig do
      public? true
      source_attribute :id
      destination_attribute :activity_id
    end
  end

  calculations do
    calculate :invite_conditions_met, :boolean do
      public? true
      description "邀请条件是否满足"
      argument :user_id, :string

      calculation fn records, context ->
        user_id = context.arguments[:user_id]

        Enum.map(records, fn record ->
          # Check if user has met invite requirements
          invite_count = get_user_invite_count(user_id)
          # Default requirement is 5 invites
          invite_count >= 5
        end)
      end
    end

    calculate :cash_claimed, :boolean do
      public? true
      description "是否已领取提现"
      argument :user_id, :string

      calculation fn records, context ->
        user_id = context.arguments[:user_id]

        Enum.map(records, fn record ->
          case get_user_participation(user_id, :invite_cash) do
            nil ->
              false

            participation ->
              get_in(participation.participation_data, ["cash_claimed"]) == true
          end
        end)
      end
    end

    calculate :invite_progress, :map do
      public? true
      description "邀请进度"
      argument :user_id, :string

      calculation fn records, context ->
        user_id = context.arguments[:user_id]

        Enum.map(records, fn record ->
          invite_count = get_user_invite_count(user_id)
          required_invites = 5

          %{
            current_invites: invite_count,
            required_invites: required_invites,
            can_withdraw: invite_count >= required_invites,
            progress_percent: calculate_cash_progress(invite_count, required_invites)
          }
        end)
      end
    end
  end

  # Helper functions for invite cash activity operations

  defp check_invite_conditions_met(user_id) do
    # Check if user has invited enough people
    invite_count = get_user_invite_count(user_id)
    # Default requirement is 5 invites
    invite_count >= 5
  end

  defp check_cash_reward_claimed(user_id) do
    case get_user_participation(user_id, :invite_cash) do
      nil ->
        false

      participation ->
        get_in(participation.participation_data, ["cash_claimed"]) == true
    end
  end

  defp get_user_invite_count(user_id) do
    # TODO: Implement actual invite count lookup
    # This should query the user's invite records
    # For now, return 0
    0
  end

  defp calculate_cash_progress(current, required) do
    min(current / required * 100, 100) |> round()
  end

  defp distribute_cash_reward(user_id, amount, description) do
    # Use ledger system to distribute reward
    from_account = "system:XAA:rewards"
    to_account = "user:XAA:#{user_id}"

    Cypridina.Ledger.game_win(from_account, to_account, amount, description)
  end

  defp record_cash_claim(user_id) do
    case get_user_participation(user_id, :invite_cash) do
      nil ->
        Teen.ActivitySystem.UserActivityParticipation.create(%{
          user_id: user_id,
          activity_type: :invite_cash,
          progress: 1,
          status: :active,
          participation_data: %{"cash_claimed" => true}
        })

      participation ->
        new_data = Map.put(participation.participation_data || %{}, "cash_claimed", true)

        Teen.ActivitySystem.UserActivityParticipation.update_progress(participation, %{
          participation_data: new_data
        })
    end
  end

  defp get_user_participation(user_id, activity_type) do
    case Teen.ActivitySystem.UserActivityParticipation.list_by_user_and_activity(
           user_id,
           activity_type
         ) do
      {:ok, [participation | _]} -> participation
      _ -> nil
    end
  end

  @doc """
  Get free cash (invite) data for a user
  """
  def get_free_cash_data(user_id) do
    try do
      # Get active invite cash activities
      case list_active_activities() do
        {:ok, [activity | _]} ->
          # Load invite progress calculation
          activity =
            Ash.load!(activity, [:invite_progress, :cash_claimed],
              actor: %{id: user_id},
              authorize?: false
            )

          progress = activity.invite_progress

          {:ok,
           %{
             free_cash_amount: Decimal.to_integer(activity.total_reward),
             initial_min: Decimal.to_integer(activity.initial_min),
             initial_max: Decimal.to_integer(activity.initial_max),
             required_invites: progress.required_invites,
             current_invites: progress.current_invites,
             can_withdraw: progress.can_withdraw,
             status: if(activity.status == :enabled, do: 1, else: 0),
             progress: progress.progress_percent
           }}

        {:ok, []} ->
          {:ok, build_default_cash_data()}

        {:error, reason} ->
          require Logger
          Logger.error("Failed to get invite cash activity: #{inspect(reason)}")
          {:ok, build_default_cash_data()}
      end
    rescue
      e ->
        require Logger
        Logger.error("Exception getting free cash data: #{inspect(e)}")
        {:ok, build_default_cash_data()}
    end
  end

  defp build_default_cash_data do
    %{
      free_cash_amount: 1000,
      required_invites: 5,
      current_invites: 0,
      can_withdraw: false,
      status: 1
    }
  end

  @doc """
  Process free cash (invite) claim
  """
  def process_free_cash_claim(user_id, _data) do
    try do
      # Get active invite cash activity
      case list_active_activities() do
        {:ok, [activity | _]} ->
          # Use the claim_cash action
          case claim_cash(activity, %{user_id: user_id}) do
            {:ok, _updated_activity} ->
              {:ok,
               %{code: 0, fetchaward: Decimal.to_integer(activity.total_reward), msg: "免费提现领取成功"}}

            {:error, error} ->
              message =
                case error do
                  %Ash.Error.Invalid{errors: [%{message: msg} | _]} -> msg
                  _ -> "奖励领取失败"
                end

              {:error, %{code: 1, msg: message}}
          end

        {:ok, []} ->
          {:error, %{code: 1, msg: "暂无免费提现活动"}}

        {:error, _} ->
          {:error, %{code: 1, msg: "获取活动配置失败"}}
      end
    rescue
      e ->
        require Logger
        Logger.error("Exception processing free cash claim: #{inspect(e)}")
        {:error, %{code: 1, msg: "系统错误"}}
    end
  end
end
