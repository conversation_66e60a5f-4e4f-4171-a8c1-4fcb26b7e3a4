defmodule Teen.ActivitySystem.UserLoginRecord do
  @moduledoc """
  用户登录记录资源

  记录用户每次登录的详细信息，用于：
  - 统计连续登录天数
  - 计算登录奖励
  - 分析用户活跃度
  """

  use Ash.Resource,
    otp_app: :cypridina,
    data_layer: AshPostgres.DataLayer,
    domain: Teen.ActivitySystem,
    extensions: [AshAdmin.Resource]

  admin do
    table_columns [
      :id,
      :user_id,
      :login_date,
      :login_time,
      :consecutive_days,
      :is_first_today,
      :ip_address,
      :device_type
    ]
  end

  postgres do
    table "user_login_records"
    repo Cypridina.Repo

    # Configure identity WHERE clause for SQL generation
    identity_wheres_to_sql unique_user_date_first: "is_first_today = true"
  end

  code_interface do
    define :create
    define :read
    define :update
    define :destroy
    define :get_today_login
    define :get_user_login_history
    define :get_consecutive_days
    define :update_consecutive_days
    define :update_login_info
  end

  actions do
    defaults [:read, :update, :destroy]

    create :create do
      accept [:user_id, :ip_address, :device_type, :client_info]

      change fn changeset, _context ->
        user_id = Ash.Changeset.get_attribute(changeset, :user_id)
        today = Date.utc_today()
        now = DateTime.utc_now()

        # 检查今天是否已经登录过
        is_first_today =
          case get_today_login(%{user_id: user_id, date: today}) do
            {:ok, []} -> true
            {:ok, [_ | _]} -> false
            _ -> true
          end

        # 计算连续登录天数
        consecutive_days =
          if is_first_today do
            calculate_consecutive_days(user_id, today)
          else
            # 如果今天已登录，获取今天的记录中的连续天数
            case get_today_login(%{user_id: user_id, date: today}) do
              {:ok, [record | _]} when not is_nil(record) -> record.consecutive_days
              _ -> 1
            end
          end

        changeset
        |> Ash.Changeset.change_attribute(:login_date, today)
        |> Ash.Changeset.change_attribute(:login_time, now)
        |> Ash.Changeset.change_attribute(:consecutive_days, consecutive_days)
        |> Ash.Changeset.change_attribute(:is_first_today, is_first_today)
      end
    end

    read :get_today_login do
      argument :user_id, :uuid, allow_nil?: false
      argument :date, :date, allow_nil?: false

      filter expr(user_id == ^arg(:user_id) and login_date == ^arg(:date))
      prepare build(limit: 1)
    end

    read :get_user_login_history do
      argument :user_id, :uuid, allow_nil?: false
      argument :start_date, :date, allow_nil?: true
      argument :end_date, :date, allow_nil?: true
      argument :limit, :integer, default: 30

      filter expr(
               user_id == ^arg(:user_id) and
                 (is_nil(^arg(:start_date)) or login_date >= ^arg(:start_date)) and
                 (is_nil(^arg(:end_date)) or login_date <= ^arg(:end_date))
             )

      prepare build(sort: [desc: :login_date], limit: arg(:limit))
    end

    read :get_consecutive_days do
      argument :user_id, :uuid, allow_nil?: false
      get? true

      filter expr(user_id == ^arg(:user_id) and login_date == ^Date.utc_today())
    end

    update :update_consecutive_days do
      argument :consecutive_days, :integer, allow_nil?: false
      change set_attribute(:consecutive_days, arg(:consecutive_days))
    end

    # 新的登录信息更新动作 - 从User资源迁移过来
    action :update_login_info do
      description "更新用户登录信息，包括连续登录天数计算和用户表更新"
      argument :user_id, :uuid, allow_nil?: false
      argument :ip_address, :string, allow_nil?: true
      argument :device_type, :string, allow_nil?: true
      argument :client_info, :map, allow_nil?: true

      run fn input, _context ->
        user_id = input.arguments.user_id
        ip_address = input.arguments.ip_address
        device_type = input.arguments.device_type
        client_info = input.arguments.client_info || %{}

        today = Date.utc_today()
        now = DateTime.utc_now()

        # 检查今天是否已经登录过
        case get_today_login(%{user_id: user_id, date: today}) do
          {:ok, [existing_record | _]} when not is_nil(existing_record) ->
            # 今天已经登录过，不需要更新任何字段
            case update_user_login_fields(user_id, now, existing_record.consecutive_days, nil) do
              {:ok, _user} ->
                :ok

              {:error, reason} ->
                {:error, reason}
            end

          {:ok, []} ->
            # 今天首次登录，创建新记录
            consecutive_days = calculate_consecutive_days(user_id, today)

            case Teen.ActivitySystem.UserLoginRecord.create(%{
                   user_id: user_id,
                   ip_address: ip_address,
                   device_type: device_type,
                   client_info: client_info
                 }) do
              {:ok, login_record} ->
                # 获取用户信息（不需要更新任何字段）
                case update_user_login_fields(user_id, now, consecutive_days, 1) do
                  {:ok, _user} ->
                    :ok

                  {:error, reason} ->
                    {:error, reason}
                end

              {:error, reason} ->
                {:error, reason}
            end

          {:error, reason} ->
            {:error, reason}
        end
      end
    end
  end

  attributes do
    uuid_primary_key :id

    attribute :user_id, :uuid do
      allow_nil? false
      public? true
      description "用户ID"
    end

    attribute :login_date, :date do
      allow_nil? false
      public? true
      description "登录日期"
    end

    attribute :login_time, :utc_datetime do
      allow_nil? false
      public? true
      description "登录时间"
    end

    attribute :consecutive_days, :integer do
      allow_nil? false
      public? true
      description "连续登录天数"
      default 1
      constraints min: 1
    end

    attribute :is_first_today, :boolean do
      allow_nil? false
      public? true
      description "是否今日首次登录"
      default true
    end

    attribute :ip_address, :string do
      allow_nil? true
      public? true
      description "登录IP地址"
      constraints max_length: 45
    end

    attribute :device_type, :string do
      allow_nil? true
      public? true
      description "设备类型"
      constraints max_length: 50
    end

    attribute :client_info, :map do
      allow_nil? true
      public? true
      description "客户端信息"
    end

    timestamps()
  end

  relationships do
    belongs_to :user, Cypridina.Accounts.User do
      public? true
      source_attribute :user_id
      destination_attribute :id
    end
  end

  identities do
    # 每个用户每天只能有一条首次登录记录
    identity :unique_user_date_first, [:user_id, :login_date, :is_first_today],
      where: expr(is_first_today == true)
  end

  # 计算连续登录天数的私有函数
  defp calculate_consecutive_days(user_id, today) do
    yesterday = Date.add(today, -1)

    # 查找昨天的登录记录
    case get_user_login_history(%{
           user_id: user_id,
           start_date: yesterday,
           end_date: yesterday,
           limit: 1
         }) do
      {:ok, [yesterday_record | _]} ->
        # 如果昨天有登录，则连续天数+1
        yesterday_record.consecutive_days + 1

      _ ->
        # 如果昨天没有登录，则连续天数重置为1
        1
    end
  end

  # 更新用户表中的登录相关字段（现在不需要更新任何字段，但保留函数以保持兼容性）
  defp update_user_login_fields(user_id, _login_time, _consecutive_days, _total_days_increment) do
    # 现在所有登录相关数据都在UserLoginRecord中管理，不需要更新User表
    # 直接返回用户信息
    Cypridina.Accounts.User.get_by_id(user_id)
  end
end
