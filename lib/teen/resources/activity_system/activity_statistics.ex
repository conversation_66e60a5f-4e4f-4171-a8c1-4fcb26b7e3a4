defmodule Teen.ActivitySystem.ActivityStatistics do
  @moduledoc """
  活动统计资源

  提供各种活动的统计分析功能
  包括参与人数、奖励发放、转化率等统计数据
  """

  use Ash.Resource,
    otp_app: :cypridina,
    data_layer: Ash.DataLayer.Ets,
    domain: Teen.ActivitySystem,
    extensions: [AshAdmin.Resource]

  admin do
    table_columns [
      :id,
      :activity_type,
      :stat_date,
      :participant_count,
      :reward_total,
      :inserted_at
    ]
  end

  code_interface do
    define :generate_daily_stats
    define :generate_activity_report
    define :get_user_activity_summary
    define :get_popular_activities
  end

  actions do
    defaults [:read]

    action :generate_daily_stats, :struct do
      argument :stat_date, :date, allow_nil?: false

      run fn input, _context ->
        # 生成指定日期的活动统计
        stat_date = input.arguments.stat_date

        # 这里实现统计逻辑
        stats = %{
          stat_date: stat_date,
          total_participants: 0,
          total_rewards: Decimal.new("0"),
          activity_breakdown: %{}
        }

        {:ok, stats}
      end
    end

    action :generate_activity_report, :struct do
      argument :activity_type, :atom, allow_nil?: false
      argument :start_date, :date, allow_nil?: false
      argument :end_date, :date, allow_nil?: false

      run fn input, _context ->
        # 生成活动报告
        args = input.arguments

        report = %{
          activity_type: args.activity_type,
          period: "#{args.start_date} - #{args.end_date}",
          total_participants: 0,
          total_rewards: Decimal.new("0"),
          conversion_rate: Decimal.new("0"),
          daily_breakdown: []
        }

        {:ok, report}
      end
    end

    action :get_user_activity_summary, :struct do
      argument :user_id, :uuid, allow_nil?: false
      argument :start_date, :date, allow_nil?: true
      argument :end_date, :date, allow_nil?: true

      run fn input, _context ->
        # 获取用户活动摘要
        user_id = input.arguments.user_id

        summary = %{
          user_id: user_id,
          total_activities_participated: 0,
          total_rewards_claimed: Decimal.new("0"),
          favorite_activity_type: nil,
          recent_activities: []
        }

        {:ok, summary}
      end
    end

    action :get_popular_activities, :struct do
      argument :period_days, :integer, allow_nil?: false, default: 30
      argument :limit, :integer, allow_nil?: false, default: 10

      run fn input, _context ->
        # 获取热门活动
        popular_activities = []

        {:ok, popular_activities}
      end
    end
  end

  resource do
    description "活动统计资源，提供各种活动的统计分析功能"

    # 默认排序
    defaults do
      sort desc: :stat_date
    end
  end

  attributes do
    uuid_primary_key :id

    attribute :activity_type, :atom do
      allow_nil? false
      public? true
      description "活动类型"
    end

    attribute :stat_date, :date do
      allow_nil? false
      public? true
      description "统计日期"
    end

    attribute :participant_count, :integer do
      allow_nil? false
      public? true
      description "参与人数"
      default 0
    end

    attribute :reward_total, :decimal do
      allow_nil? false
      public? true
      description "奖励总额（分）"
      default Decimal.new("0")
    end

    attribute :conversion_rate, :decimal do
      allow_nil? false
      public? true
      description "转化率（%）"
      default Decimal.new("0")
    end

    attribute :additional_data, :map do
      allow_nil? true
      public? true
      description "额外统计数据（JSON）"
    end

    timestamps()
  end
end
