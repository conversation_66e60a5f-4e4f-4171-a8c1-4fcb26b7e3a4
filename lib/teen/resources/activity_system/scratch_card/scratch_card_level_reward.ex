defmodule Teen.ActivitySystem.ScratchCardLevelReward do
  @moduledoc """
  刮刮卡等级奖励配置资源

  管理刮刮卡活动等级的奖励配置
  包括奖励类型、最小奖励、最大奖励、实际最大奖励、概率等
  """

  use Ash.Resource,
    otp_app: :cypridina,
    data_layer: AshPostgres.DataLayer,
    domain: Teen.ActivitySystem,
    extensions: [AshAdmin.Resource]

  admin do
    table_columns [
      :id,
      :task_level_id,
      :reward_type,
      :min_reward,
      :max_reward,
      :probability,
      :sort_order,
      :updated_at
    ]
  end

  postgres do
    table "scratch_card_level_rewards"
    repo Cyp<PERSON>ina.Repo
  end

  code_interface do
    define :create
    define :read
    define :update
    define :destroy
    define :list_by_level
    define :get_random_reward
  end

  actions do
    defaults [:read, :update, :destroy]

    create :create do
      accept [
        :task_level_id,
        :reward_type,
        :min_reward,
        :max_reward,
        :actual_max_reward,
        :probability,
        :sort_order
      ]
    end

    read :list_by_level do
      argument :task_level_id, :uuid, allow_nil?: false
      filter expr(task_level_id == ^arg(:task_level_id))
      prepare build(sort: [:sort_order])
    end

    read :get_random_reward do
      argument :task_level_id, :uuid, allow_nil?: false
      filter expr(task_level_id == ^arg(:task_level_id))
      prepare build(sort: [:sort_order])
    end
  end

  attributes do
    uuid_primary_key :id

    attribute :reward_type, :atom do
      allow_nil? false
      public? true
      description "奖励类型"
      constraints one_of: [:coins, :cash]
    end

    attribute :min_reward, :decimal do
      allow_nil? false
      public? true
      description "最小奖励（分）"
      constraints min: Decimal.new("0")
    end

    attribute :max_reward, :decimal do
      allow_nil? false
      public? true
      description "最大奖励（展示）（分）"
      constraints min: Decimal.new("0")
    end

    attribute :actual_max_reward, :decimal do
      allow_nil? false
      public? true
      description "实际最大奖励（计算）（分）"
      constraints min: Decimal.new("0")
    end

    attribute :probability, :decimal do
      allow_nil? false
      public? true
      description "概率（%）"
      constraints min: Decimal.new("0"), max: Decimal.new("100")
    end

    attribute :sort_order, :integer do
      allow_nil? false
      public? true
      description "排序号"
      default 0
    end

    timestamps()
  end

  relationships do
    belongs_to :task_level, Teen.ActivitySystem.ScratchCardTaskLevel do
      public? true
      source_attribute :task_level_id
      destination_attribute :id
    end
  end
end
