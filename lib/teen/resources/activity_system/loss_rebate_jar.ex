defmodule Teen.ActivitySystem.LossRebateJar do
  @moduledoc """
  输钱返利金罐子资源

  管理输钱返利配置
  规则：每日领取前一天损失的指定百分比
  """

  use Ash.Resource,
    otp_app: :cypridina,
    data_layer: AshPostgres.DataLayer,
    domain: Teen.ActivitySystem,
    extensions: [AshAdmin.Resource]

  admin do
    table_columns [
      :id,
      :title,
      :max_claims,
      :loss_threshold,
      :rebate_percentage,
      :max_rebate,
      :status,
      :updated_at
    ]
  end

  postgres do
    table "loss_rebate_jars"
    repo Cypridina.Repo
  end

  code_interface do
    define :create
    define :read
    define :update
    define :destroy
    define :list_active_jars
    define :get_rebate_config
    define :enable_jar
    define :disable_jar
  end

  actions do
    defaults [:read, :update, :destroy]

    create :create do
      accept [:title, :max_claims, :loss_threshold, :rebate_percentage, :max_rebate, :status]

      change fn changeset, _context ->
        changeset
        |> Ash.Changeset.change_attribute(:status, :enabled)
        |> Ash.Changeset.change_attribute(:rebate_percentage, Decimal.new("10"))
      end
    end

    read :list_active_jars do
      filter expr(status == :enabled)
    end

    read :get_rebate_config do
      get? true
      filter expr(status == :enabled)
    end

    update :enable_jar do
      require_atomic? false
      change set_attribute(:status, :enabled)
    end

    update :disable_jar do
      require_atomic? false
      change set_attribute(:status, :disabled)
    end
  end

  attributes do
    uuid_primary_key :id

    attribute :title, :string do
      allow_nil? false
      public? true
      description "标题"
      constraints max_length: 100
    end

    # 为了与 live 文件兼容，添加别名字段
    attribute :rebate_name, :string do
      allow_nil? false
      public? true
      description "返利名称（title 的别名）"
      constraints max_length: 100
    end

    attribute :max_claims, :integer do
      allow_nil? false
      public? true
      description "领取次数"
      constraints min: 1
      default 1
    end

    attribute :loss_threshold, :decimal do
      allow_nil? false
      public? true
      description "用户输钱金币值（分）"
      constraints min: Decimal.new("0")
    end

    # 为了与 live 文件兼容，添加别名字段
    attribute :min_loss_amount, :decimal do
      allow_nil? false
      public? true
      description "最小亏损金额（loss_threshold 的别名）"
      constraints min: Decimal.new("0")
    end

    attribute :rebate_percentage, :decimal do
      allow_nil? false
      public? true
      description "输钱奖励（百分比）"
      constraints min: Decimal.new("0"), max: Decimal.new("100")
      default Decimal.new("10")
    end

    attribute :max_rebate, :decimal do
      allow_nil? false
      public? true
      description "奖励上限（分）"
      constraints min: Decimal.new("0")
    end

    # 为了与 live 文件兼容，添加别名字段
    attribute :max_rebate_amount, :decimal do
      allow_nil? false
      public? true
      description "最大返利金额（max_rebate 的别名）"
      constraints min: Decimal.new("0")
    end

    attribute :calculation_period, :atom do
      allow_nil? false
      public? true
      description "计算周期"
      constraints one_of: [:daily, :weekly, :monthly]
      default :daily
    end

    attribute :rebate_type, :atom do
      allow_nil? false
      public? true
      description "返利类型"
      constraints one_of: [:coins, :points, :cash]
      default :coins
    end

    attribute :auto_distribute, :boolean do
      allow_nil? false
      public? true
      description "自动发放"
      default false
    end

    attribute :is_active, :boolean do
      allow_nil? false
      public? true
      description "是否激活"
      default true
    end

    attribute :status, :atom do
      allow_nil? false
      public? true
      description "状态"
      constraints one_of: [:enabled, :disabled]
      default :enabled
    end

    timestamps()
  end
end
