defmodule Teen.ActivitySystem.Cdkey do
  @moduledoc """
  CDKEY资源

  管理CDKEY的基本配置，包括代码、使用次数、奖励配置等
  """

  require Logger

  use Ash.Resource,
    otp_app: :cypridina,
    data_layer: AshPostgres.DataLayer,
    domain: Teen.ActivitySystem,
    extensions: [AshAdmin.Resource]

  admin do
    table_columns [:id, :code, :status, :used_count, :max_uses, :creator, :valid_from, :valid_to]
  end

  postgres do
    table "cdkeys"
    repo Cypridina.Repo
  end

  code_interface do
    define :create
    define :read
    define :update
    define :destroy
    define :list
    define :by_code, args: [:code]
    define :active_codes
    define :expired_codes
    define :use_code
  end

  actions do
    defaults [:read, :destroy]

    create :create do
      accept [:code, :max_uses, :rewards, :valid_from, :valid_to, :creator]

      validate present([:code, :rewards, :valid_from, :valid_to, :creator])

      validate match(:code, ~r/^[A-Z0-9\-]{6,20}$/) do
        message "代码格式无效"
      end

      change fn changeset, _context ->
        changeset
        |> Ash.Changeset.change_attribute(:status, :active)
        |> Ash.Changeset.change_attribute(:used_count, 0)
      end
    end

    read :list do
      # 基本的列表查询动作
    end

    read :by_code do
      argument :code, :string, allow_nil?: false
      filter expr(code == ^arg(:code))
    end

    read :active_codes do
      filter expr(
               status == :active and valid_from <= ^DateTime.utc_now() and
                 valid_to >= ^DateTime.utc_now()
             )
    end

    read :expired_codes do
      filter expr(status == :expired or valid_to < ^DateTime.utc_now())
    end

    update :use_code do
      require_atomic? false
      change increment(:used_count)

      change fn changeset, _context ->
        current_used = Ash.Changeset.get_attribute(changeset, :used_count) || 0
        max_uses = Ash.Changeset.get_attribute(changeset, :max_uses)

        if current_used >= max_uses do
          Ash.Changeset.change_attribute(changeset, :status, :inactive)
        else
          changeset
        end
      end
    end

    update :update do
      accept [:code, :status, :max_uses, :rewards, :creator, :valid_from, :valid_to, :used_count]
      primary? true
      require_atomic? false

      # 添加对 InlineCRUD 辅助字段的支持
      argument :rewards_delete, {:array, :string},
        allow_nil?: true,
        default: [],
        constraints: [items: [allow_empty?: true]]

      argument :rewards_order, {:array, :string},
        allow_nil?: true,
        default: [],
        constraints: [items: [allow_empty?: true]]

      # 使用正确的具名函数语法
      change &__MODULE__.handle_rewards_update/2
    end
  end

  resource do
    description "CDKEY资源，管理CDKEY的基本配置，包括代码、使用次数、奖励配置等"

    # 默认排序
    defaults do
      sort desc: :inserted_at
    end

    # 默认加载关联
    defaults do
      load [:claim_records]
    end
  end

  validations do
    validate compare(:valid_to, greater_than: :valid_from), message: "失效时间必须晚于生效时间"
    validate compare(:used_count, less_than_or_equal_to: :max_uses), message: "使用次数不能超过最大限制"

    validate match(:code, ~r/^[A-Z0-9\-]{6,20}$/) do
      message "代码格式无效，必须是6-20位大写字母、数字和连字符组合"
    end
  end

  attributes do
    uuid_primary_key :id

    attribute :code, :string do
      allow_nil? false
      public? true
      description "兑换码"
      constraints max_length: 50
    end

    attribute :max_uses, :integer do
      allow_nil? false
      public? true
      description "最大使用次数"
      constraints min: 1
      default 1
    end

    attribute :rewards, {:array, Teen.Reward} do
      allow_nil? false
      public? true
      description "奖励配置列表"
      default []
    end

    attribute :valid_from, :utc_datetime do
      allow_nil? false
      public? true
      description "生效时间"
    end

    attribute :valid_to, :utc_datetime do
      allow_nil? false
      public? true
      description "失效时间"
    end

    attribute :creator, :string do
      allow_nil? false
      public? true
      description "创建人"
      constraints max_length: 100
    end

    attribute :status, :atom do
      allow_nil? false
      public? true
      description "状态"
      constraints one_of: [:active, :inactive, :expired]
      default :active
    end

    attribute :used_count, :integer do
      allow_nil? false
      public? true
      description "已使用次数"
      default 0
      constraints min: 0
    end

    timestamps()
  end

  relationships do
    has_many :claim_records, Teen.ActivitySystem.CdkeyClaimRecord do
      public? true
      source_attribute :id
      destination_attribute :cdkey_id
    end
  end

  identities do
    identity :unique_code, [:code]
  end

  def handle_rewards_update(changeset, _context) do
    Logger.debug("删除CDKEY: #{inspect(Ash.Changeset.get_argument(changeset, :rewards_delete))}")
    Logger.debug("更新CDKEY: #{inspect(Ash.Changeset.get_argument(changeset, :rewards_order))}")

    delete_indices =
      (Ash.Changeset.get_argument(changeset, :rewards_delete) || [])
      |> Enum.reject(&(&1 == "" or is_nil(&1)))

    order_indices = Ash.Changeset.get_argument(changeset, :rewards_order) || []
    current_rewards = Ash.Changeset.get_attribute(changeset, :rewards) || []

    # 1. 检查是否需要添加新的 reward（order_indices 包含 "on"）
    should_add_new = Enum.any?(order_indices, &(&1 == "on"))

    updated_rewards =
      if should_add_new do
        # 添加一个新的空 reward
        new_reward = %Teen.Reward{
          type: :coins,
          amount: 0,
          description: nil,
          metadata: %{}
        }

        current_rewards ++ [new_reward]
      else
        current_rewards
      end

    # 2. 从对应位置删除 reward（如果 delete_indices 有值）
    final_rewards =
      if Enum.empty?(delete_indices) do
        updated_rewards
      else
        updated_rewards
        |> Enum.with_index()
        |> Enum.reject(fn {_reward, index} ->
          to_string(index) in delete_indices
        end)
        |> Enum.map(fn {reward, _index} -> reward end)
      end

    Logger.debug("最终 rewards: #{inspect(final_rewards)}")

    # 使用 force_change_attribute 确保变更被应用
    # 同时确保 params 也被更新
    updated_changeset = Ash.Changeset.force_change_attribute(changeset, :rewards, final_rewards)

    # 确保 params 也包含更新后的 rewards
    params = updated_changeset.params || %{}
    updated_params = Map.put(params, "rewards", final_rewards)
    %{updated_changeset | params: updated_params}
  end
end
