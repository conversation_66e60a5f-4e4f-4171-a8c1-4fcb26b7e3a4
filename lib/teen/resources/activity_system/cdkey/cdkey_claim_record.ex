defmodule Teen.ActivitySystem.CdkeyClaimRecord do
  @moduledoc """
  CDKEY领取记录资源

  记录用户领取CDKEY的详细信息
  包括用户ID、用户账户、领取金币、领取时间等
  """

  use Ash.Resource,
    otp_app: :cypridina,
    data_layer: AshPostgres.DataLayer,
    domain: Teen.ActivitySystem,
    extensions: [AshAdmin.Resource]

  admin do
    table_columns [:id, :cdkey_id, :user_id, :username, :claimed_coins, :claimed_at]
  end

  postgres do
    table "cdkey_claim_records"
    repo Cypridina.Repo
  end

  code_interface do
    define :create
    define :read
    define :update
    define :destroy
    define :list_by_cdkey
    define :list_by_user
    define :get_user_claim_stats
    define :by_user_and_cdkey, args: [:user_id, :cdkey_id]
  end

  actions do
    defaults [:read, :update, :destroy]

    create :create do
      accept [:cdkey_id, :user_id, :username, :cdkey_code, :claimed_coins]

      change fn changeset, _context ->
        changeset
        |> Ash.Changeset.change_attribute(:claimed_at, DateTime.utc_now())
      end
    end

    read :by_user_and_cdkey do
      argument :user_id, :uuid, allow_nil?: false
      argument :cdkey_id, :uuid, allow_nil?: false
      filter expr(user_id == ^arg(:user_id) and cdkey_id == ^arg(:cdkey_id))
    end

    read :list_by_cdkey do
      argument :cdkey_id, :uuid, allow_nil?: false
      filter expr(cdkey_id == ^arg(:cdkey_id))
      prepare build(sort: [desc: :claimed_at])
    end

    read :list_by_cdkey_code do
      argument :cdkey_code, :string, allow_nil?: false
      filter expr(cdkey_code == ^arg(:cdkey_code))
      prepare build(sort: [desc: :claimed_at])
    end

    read :list_by_user do
      argument :user_id, :uuid, allow_nil?: false
      filter expr(user_id == ^arg(:user_id))
      prepare build(sort: [desc: :claimed_at])
    end

    read :get_user_claim_stats do
      argument :user_id, :uuid, allow_nil?: false
      filter expr(user_id == ^arg(:user_id))
    end
  end

  attributes do
    uuid_primary_key :id

    attribute :user_id, :uuid do
      allow_nil? false
      public? true
      description "用户ID"
    end

    attribute :username, :string do
      allow_nil? false
      public? true
      description "用户账户"
      constraints max_length: 100
    end

    attribute :cdkey_code, :string do
      allow_nil? false
      public? true
      description "CDKEY代码"
      constraints max_length: 50
    end

    attribute :claimed_coins, :decimal do
      allow_nil? false
      public? true
      description "领取金币（分）"
      constraints min: Decimal.new("0")
    end

    attribute :claimed_at, :utc_datetime do
      allow_nil? false
      public? true
      description "领取时间"
    end

    timestamps()
  end

  relationships do
    belongs_to :cdkey, Teen.ActivitySystem.Cdkey do
      public? true
      source_attribute :cdkey_id
      destination_attribute :id
    end

    belongs_to :user, Cypridina.Accounts.User do
      public? true
      source_attribute :user_id
      destination_attribute :id
    end
  end

  identities do
    identity :unique_user_cdkey, [:user_id, :cdkey_id]
    identity :unique_user_cdkey_code, [:user_id, :cdkey_code]
  end
end
