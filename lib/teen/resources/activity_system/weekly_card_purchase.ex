defmodule Teen.ActivitySystem.WeeklyCardPurchase do
  @moduledoc """
  周卡购买记录资源

  追踪用户周卡的购买状态、有效期、每日领取记录等
  """

  use Ash.Resource,
    otp_app: :cypridina,
    data_layer: AshPostgres.DataLayer,
    domain: Teen.ActivitySystem,
    extensions: [AshAdmin.Resource]

  admin do
    table_columns [
      :id,
      :user_id,
      :card_id,
      :purchase_amount,
      :purchase_status,
      :start_date,
      :end_date,
      :daily_claims_count,
      :last_claim_date,
      :purchased_at
    ]
  end

  postgres do
    table "weekly_card_purchases"
    repo Cypridina.Repo
  end

  code_interface do
    define :create
    define :read
    define :update
    define :destroy
    define :list_by_user
    define :list_by_card
    define :get_user_card_purchase
    define :check_user_purchased_card
    define :activate_card
    define :claim_daily_reward
    define :expire_card
    define :get_active_card_purchases
  end

  actions do
    defaults [:read, :update, :destroy]

    create :create do
      accept [
        :user_id,
        :card_id,
        :purchase_amount,
        :payment_order_id,
        :start_date,
        :end_date
      ]

      change fn changeset, _context ->
        changeset
        |> Ash.Changeset.change_attribute(:purchase_status, :pending)
        |> Ash.Changeset.change_attribute(:daily_claims_count, 0)
        |> Ash.Changeset.change_attribute(:purchased_at, DateTime.utc_now())
      end
    end

    read :list_by_user do
      argument :user_id, :uuid, allow_nil?: false
      filter expr(user_id == ^arg(:user_id))
      prepare build(sort: [desc: :purchased_at])
    end

    read :list_by_card do
      argument :card_id, :uuid, allow_nil?: false
      filter expr(card_id == ^arg(:card_id))
      prepare build(sort: [desc: :purchased_at])
    end

    read :get_user_card_purchase do
      argument :user_id, :uuid, allow_nil?: false
      argument :card_id, :uuid, allow_nil?: false
      get? true

      filter expr(
               user_id == ^arg(:user_id) and
                 card_id == ^arg(:card_id) and
                 purchase_status == :active
             )
    end

    read :check_user_purchased_card do
      argument :user_id, :uuid, allow_nil?: false
      argument :card_id, :uuid, allow_nil?: false

      filter expr(
               user_id == ^arg(:user_id) and
                 card_id == ^arg(:card_id) and
                 purchase_status == :active and
                 end_date >= ^Date.utc_today()
             )
    end

    read :get_active_card_purchases do
      argument :user_id, :uuid, allow_nil?: false

      filter expr(
               user_id == ^arg(:user_id) and
                 purchase_status == :active and
                 end_date >= ^Date.utc_today()
             )
    end

    update :activate_card do
      require_atomic? false
      accept [:transaction_id]

      change fn changeset, _context ->
        changeset
        |> Ash.Changeset.change_attribute(:purchase_status, :active)
        |> Ash.Changeset.change_attribute(:activated_at, DateTime.utc_now())
      end
    end

    update :claim_daily_reward do
      require_atomic? false

      validate &claim_daily_reward_validator/2

      change fn changeset, _context ->
        purchase = changeset.data
        today = Date.utc_today()

        changeset
        |> Ash.Changeset.change_attribute(:daily_claims_count, purchase.daily_claims_count + 1)
        |> Ash.Changeset.change_attribute(:last_claim_date, today)
        |> Ash.Changeset.change_attribute(:last_claimed_at, DateTime.utc_now())
      end
    end

    update :expire_card do
      require_atomic? false

      change fn changeset, _context ->
        changeset
        |> Ash.Changeset.change_attribute(:purchase_status, :expired)
        |> Ash.Changeset.change_attribute(:expired_at, DateTime.utc_now())
      end
    end
  end

  attributes do
    uuid_primary_key :id

    attribute :user_id, :uuid do
      allow_nil? false
      public? true
      description "用户ID"
    end

    attribute :card_id, :uuid do
      allow_nil? false
      public? true
      description "周卡ID"
    end

    attribute :purchase_amount, :decimal do
      allow_nil? false
      public? true
      description "购买金额（分）"
      constraints min: Decimal.new("0")
    end

    attribute :payment_order_id, :string do
      allow_nil? true
      public? true
      description "支付订单ID"
      constraints max_length: 100
    end

    attribute :transaction_id, :string do
      allow_nil? true
      public? true
      description "交易ID"
      constraints max_length: 100
    end

    attribute :purchase_status, :atom do
      allow_nil? false
      public? true
      description "购买状态"
      constraints one_of: [:pending, :active, :expired, :refunded]
      default :pending
    end

    attribute :start_date, :date do
      allow_nil? false
      public? true
      description "开始日期"
    end

    attribute :end_date, :date do
      allow_nil? false
      public? true
      description "结束日期"
    end

    attribute :daily_claims_count, :integer do
      allow_nil? false
      public? true
      description "每日领取次数"
      default 0
      constraints min: 0
    end

    attribute :last_claim_date, :date do
      allow_nil? true
      public? true
      description "最后领取日期"
    end

    attribute :purchased_at, :utc_datetime do
      allow_nil? false
      public? true
      description "购买时间"
    end

    attribute :activated_at, :utc_datetime do
      allow_nil? true
      public? true
      description "激活时间"
    end

    attribute :last_claimed_at, :utc_datetime do
      allow_nil? true
      public? true
      description "最后领取时间"
    end

    attribute :expired_at, :utc_datetime do
      allow_nil? true
      public? true
      description "过期时间"
    end

    timestamps()
  end

  relationships do
    belongs_to :user, Cypridina.Accounts.User do
      public? true
      source_attribute :user_id
      destination_attribute :id
    end

    belongs_to :weekly_card, Teen.ActivitySystem.WeeklyCard do
      public? true
      source_attribute :card_id
      destination_attribute :id
    end
  end

  # Private functions for validations
  defp claim_daily_reward_validator(changeset, _context) do
    purchase = changeset.data
    today = Date.utc_today()

    cond do
      Date.compare(today, purchase.end_date) == :gt ->
        {:error, field: :end_date, message: "周卡已过期"}

      purchase.last_claim_date && Date.compare(purchase.last_claim_date, today) == :eq ->
        {:error, field: :last_claim_date, message: "今日奖励已领取"}

      true ->
        :ok
    end
  end
end
