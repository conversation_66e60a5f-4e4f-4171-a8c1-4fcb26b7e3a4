defmodule Teen.ActivitySystem.UserGameStatistics do
  @moduledoc """
  用户游戏统计资源

  记录用户游戏相关的统计数据：
  - 连胜次数
  - 损失金额
  - 累计充值
  - 游戏局数
  - 胜利次数
  """

  use Ash.Resource,
    otp_app: :cypridina,
    data_layer: AshPostgres.DataLayer,
    domain: Teen.ActivitySystem,
    extensions: [AshAdmin.Resource]

  admin do
    table_columns [
      :id,
      :user_id,
      :current_win_streak,
      :max_win_streak,
      :total_loss_amount,
      :cumulative_recharge,
      :total_games_played,
      :total_wins,
      :updated_at
    ]
  end

  postgres do
    table "user_game_statistics"
    repo Cypridina.Repo
  end

  code_interface do
    define :create
    define :read
    define :update
    define :destroy
    define :get_by_user, args: [:user_id]
    define :update_win_streak
    define :reset_win_streak
    define :update_loss_amount
    define :update_recharge_amount
    define :update_game_count
  end

  actions do
    defaults [:read, :destroy]

    create :create do
      accept [:user_id]

      change fn changeset, _context ->
        changeset
        |> Ash.Changeset.change_attribute(:current_win_streak, 0)
        |> Ash.Changeset.change_attribute(:max_win_streak, 0)
        |> Ash.Changeset.change_attribute(:total_loss_amount, Decimal.new("0"))
        |> Ash.Changeset.change_attribute(:cumulative_recharge, Decimal.new("0"))
        |> Ash.Changeset.change_attribute(:total_games_played, 0)
        |> Ash.Changeset.change_attribute(:total_wins, 0)
      end
    end

    update :update do
      accept [
        :current_win_streak,
        :max_win_streak,
        :total_loss_amount,
        :cumulative_recharge,
        :total_games_played,
        :total_wins
      ]

      require_atomic? false
    end

    read :get_by_user do
      argument :user_id, :uuid, allow_nil?: false
      get? true
      filter expr(user_id == ^arg(:user_id))
    end

    update :update_win_streak do
      argument :streak_count, :integer, allow_nil?: false
      require_atomic? false

      change fn changeset, context ->
        streak_count = Ash.Changeset.get_argument(changeset, :streak_count)
        current_max = changeset.data.max_win_streak || 0

        changeset
        |> Ash.Changeset.change_attribute(:current_win_streak, streak_count)
        |> Ash.Changeset.change_attribute(:max_win_streak, max(streak_count, current_max))
      end
    end

    update :reset_win_streak do
      require_atomic? false

      change fn changeset, _context ->
        changeset
        |> Ash.Changeset.change_attribute(:current_win_streak, 0)
      end
    end

    update :update_loss_amount do
      argument :amount, :decimal, allow_nil?: false
      require_atomic? false

      change fn changeset, context ->
        amount = Ash.Changeset.get_argument(changeset, :amount)
        current_total = changeset.data.total_loss_amount || Decimal.new("0")

        changeset
        |> Ash.Changeset.change_attribute(:total_loss_amount, Decimal.add(current_total, amount))
      end
    end

    update :update_recharge_amount do
      argument :amount, :decimal, allow_nil?: false
      require_atomic? false

      change fn changeset, context ->
        amount = Ash.Changeset.get_argument(changeset, :amount)
        current_total = changeset.data.cumulative_recharge || Decimal.new("0")

        changeset
        |> Ash.Changeset.change_attribute(
          :cumulative_recharge,
          Decimal.add(current_total, amount)
        )
      end
    end

    update :update_game_count do
      argument :games_delta, :integer, allow_nil?: false
      argument :wins_delta, :integer, allow_nil?: false
      require_atomic? false

      change fn changeset, context ->
        games_delta = Ash.Changeset.get_argument(changeset, :games_delta)
        wins_delta = Ash.Changeset.get_argument(changeset, :wins_delta)

        current_games = changeset.data.total_games_played || 0
        current_wins = changeset.data.total_wins || 0

        changeset
        |> Ash.Changeset.change_attribute(:total_games_played, current_games + games_delta)
        |> Ash.Changeset.change_attribute(:total_wins, current_wins + wins_delta)
      end
    end
  end

  validations do
    validate compare(:current_win_streak, greater_than_or_equal_to: 0),
      message: "当前连胜次数不能为负数"

    validate compare(:max_win_streak, greater_than_or_equal_to: 0),
      message: "最大连胜次数不能为负数"

    validate compare(:total_loss_amount, greater_than_or_equal_to: Decimal.new("0")),
      message: "总损失金额不能为负数"

    validate compare(:cumulative_recharge, greater_than_or_equal_to: Decimal.new("0")),
      message: "累计充值金额不能为负数"

    validate compare(:total_games_played, greater_than_or_equal_to: 0),
      message: "总游戏局数不能为负数"

    validate compare(:total_wins, greater_than_or_equal_to: 0),
      message: "总胜利次数不能为负数"
  end

  attributes do
    uuid_primary_key :id

    attribute :user_id, :uuid do
      allow_nil? false
      public? true
      description "用户ID"
    end

    attribute :current_win_streak, :integer do
      allow_nil? false
      public? true
      description "当前连胜次数"
      default 0
      constraints min: 0
    end

    attribute :max_win_streak, :integer do
      allow_nil? false
      public? true
      description "最大连胜次数"
      default 0
      constraints min: 0
    end

    attribute :total_loss_amount, :decimal do
      allow_nil? false
      public? true
      description "总损失金额（分）"
      default Decimal.new("0")
      constraints min: Decimal.new("0")
    end

    attribute :cumulative_recharge, :decimal do
      allow_nil? false
      public? true
      description "累计充值金额（分）"
      default Decimal.new("0")
      constraints min: Decimal.new("0")
    end

    attribute :total_games_played, :integer do
      allow_nil? false
      public? true
      description "总游戏局数"
      default 0
      constraints min: 0
    end

    attribute :total_wins, :integer do
      allow_nil? false
      public? true
      description "总胜利次数"
      default 0
      constraints min: 0
    end

    timestamps()
  end

  relationships do
    belongs_to :user, Cypridina.Accounts.User do
      public? true
      source_attribute :user_id
      destination_attribute :id
    end
  end

  identities do
    identity :unique_user, [:user_id]
  end

  @doc """
  获取或创建用户统计记录
  """
  def get_or_create_user_stats(user_id) do
    case get_by_user(user_id) do
      {:ok, stats} when not is_nil(stats) ->
        {:ok, stats}

      {:ok, nil} ->
        create(%{user_id: user_id})

      {:error, reason} ->
        {:error, reason}
    end
  end

  @doc """
  更新用户连胜统计
  """
  def update_user_win_streak(user_id, is_win) do
    case get_or_create_user_stats(user_id) do
      {:ok, stats} ->
        if is_win do
          new_streak = stats.current_win_streak + 1
          update_win_streak(stats, %{streak_count: new_streak})
        else
          reset_win_streak(stats)
        end

      {:error, reason} ->
        {:error, reason}
    end
  end

  @doc """
  更新用户损失金额
  """
  def update_user_loss(user_id, loss_amount) do
    case get_or_create_user_stats(user_id) do
      {:ok, stats} ->
        update_loss_amount(stats, %{amount: loss_amount})

      {:error, reason} ->
        {:error, reason}
    end
  end

  @doc """
  更新用户充值金额
  """
  def update_user_recharge(user_id, recharge_amount) do
    case get_or_create_user_stats(user_id) do
      {:ok, stats} ->
        update_recharge_amount(stats, %{amount: recharge_amount})

      {:error, reason} ->
        {:error, reason}
    end
  end

  @doc """
  更新用户游戏统计
  """
  def update_user_game_stats(user_id, games_delta, wins_delta) do
    case get_or_create_user_stats(user_id) do
      {:ok, stats} ->
        update_game_count(stats, %{games_delta: games_delta, wins_delta: wins_delta})

      {:error, reason} ->
        {:error, reason}
    end
  end
end
