defmodule Teen.Reward do
  use Ash.Resource,
    data_layer: :embedded

  # 如果需要更新操作
  actions do
    defaults [:read, :destroy]

    create :create do
      primary? true
      accept [:type, :amount, :description, :metadata]
    end

    update :update do
      primary? true
      accept [:type, :amount, :description, :metadata]
    end
  end

  attributes do
    attribute :type, :atom do
      allow_nil? false
      constraints one_of: [:coins, :items, :experience]
    end

    attribute :amount, :integer do
      allow_nil? false
      default 0
    end

    attribute :description, :string
    attribute :metadata, :map, default: %{}
  end
end

defmodule Teen.Types.Reward do
  @moduledoc """
  奖励结构体定义


  用于统一表示系统中的各种奖励类型
  """

  # use Ash.Type.NewType,
  #   subtype_of: :map,
  #   constraints: [
  #     fields: [
  #       type: [
  #         type: :atom,
  #         allow_nil?: false
  #       ],
  #       amount: [
  #         type: :integer,
  #         allow_nil?: false
  #       ],
  #       description: [
  #         type: :string,
  #         allow_nil?: true
  #       ],
  #       metadata: [
  #         type: :map,
  #         allow_nil?: false
  #       ]
  #     ]
  #   ]

  use Ash.TypedStruct

  typed_struct do
    field :type, :atom, allow_nil?: false
    field :amount, :integer, allow_nil?: false
    field :description, :string
    field :metadata, :map
  end

  #   defstruct [
  #   :type,
  #   :amount,
  #   :description,
  #   :metadata
  # ]

  @type reward_type ::
          :bonus_money
          # 现金
          | :cash
          # VIP经验值
          | :vip_experience
          # 道具
          | :items

  @type t :: %__MODULE__{
          type: reward_type(),
          amount: integer(),
          description: String.t() | nil,
          metadata: map()
        }

  @valid_types [:bonus_money, :cash, :vip_experience, :items]

  @doc """
  创建新的奖励结构体
  """
  def new(type, amount, opts \\ []) do
    %{
      type: type,
      amount: if(is_integer(amount), do: amount, else: String.to_integer(to_string(amount))),
      description: Keyword.get(opts, :description),
      metadata: Keyword.get(opts, :metadata, %{})
    }
  end

  @doc """
  验证奖励类型是否有效
  """
  def valid_type?(type) do
    type in @valid_types
  end

  @doc """
  获取所有有效的奖励类型
  """
  def all_types, do: @valid_types
end
