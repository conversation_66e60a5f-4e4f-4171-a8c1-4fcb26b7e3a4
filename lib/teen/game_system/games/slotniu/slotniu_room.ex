defmodule <PERSON><PERSON><PERSON><PERSON>.Teen.GameSystem.Games.SlotNiu.SlotNiuRoom do
  @moduledoc """
  SlotNiu老虎机游戏房间实现 - 参考slot777的单机游戏模式
  """

  use <PERSON><PERSON><PERSON><PERSON>.Teen.GameSystem.RoomBase, game_type: :<PERSON><PERSON><PERSON>
  require Logger

  alias <PERSON><PERSON><PERSON><PERSON>.Teen.GameSystem.Games.SlotNiu.SlotNiuLogic
  alias <PERSON><PERSON><PERSON>ina.Teen.GameSystem.Games.SlotNiu.SlotNiuConfig
  alias <PERSON><PERSON><PERSON><PERSON>.Accounts
  alias CypridinaWeb.GameChannel
  alias Teen.GameSystem.JackpotManager

  # 启动房间进程
  def start_link(room_config) do
    GenServer.start_link(__MODULE__, room_config, name: via_tuple(room_config.id))
  end

  # 通过房间ID获取进程名称
  defp via_tuple(room_id) do
    {:via, Registry, {Cypridina.RoomRegistry, room_id}}
  end

  # 协议ID定义 (与前端TpMasterClient保持一致)
  @protocol_ids %{
    # 客户端发送
    # 游戏开始
    cs_gamestart: 1000,
    # 转盘开始
    cs_spinstart: 1003,
    # Jackpot中奖列表请求
    cs_jplist: 1005,

    # 服务端发送
    # 游戏开始结果
    sc_gamestart: 1001,
    # 免费游戏结果
    sc_freegame: 1002,
    # 转盘开始结果
    sc_spinstart: 1004,
    # Jackpot中奖列表
    sc_jplist: 1006,
    # 同步Jackpot分数
    sc_jackpot: 1007,
    # Jackpot中奖通知
    sc_jpaward: 1008,
    # 广播桌面玩家列表
    sc_playerlist: 1009,
    # 广播桌面玩家游戏结果
    sc_gameresult: 1010
  }

  # 动态获取Jackpot金额（从真实奖池系统获取）
  defp get_jackpot_amount_safe(state \\ nil) do
    try do
      if state && state.game_id do
        # 使用RoomBase提供的get_jackpot_balance函数
        get_jackpot_balance(state, :jackpot)
      else
        # 如果没有state，返回默认值
        # 默认Jackpot金额 (参考IndiaGameServer配置：15000000)
        15_000_000
      end
    rescue
      error ->
        Logger.warning("🎰 [SLOTNIU] 获取Jackpot奖池余额失败: #{inspect(error)}, 使用默认值")
        # 出错时使用默认值
        15_000_000
    end
  end

  # 安全获取Prize奖池金额
  defp get_prize_amount_safe do
    try do
      # Prize奖池初始为0，通过游戏累积
      0
    rescue
      _ -> 0
    end
  end

  # 注释：Prize奖池现在使用房间状态中的prize_pool字段管理
  # 不再需要单独的玩家Prize管理函数

  # 引用全局SlotNiu Jackpot管理器
  alias Cypridina.Teen.GameSystem.Games.SlotNiu.SlotNiuJackpot

  # 确保SlotNiu Jackpot进程已启动
  defp ensure_slotniu_jackpot_started do
    case GenServer.whereis(SlotNiuJackpot) do
      nil ->
        Logger.info("🎰 [SLOTNIU_JACKPOT] 启动SlotNiu Jackpot进程")

        case SlotNiuJackpot.start_link([]) do
          {:ok, _pid} ->
            Logger.info("🎰 [SLOTNIU_JACKPOT] ✅ SlotNiu Jackpot进程启动成功")
            :ok

          {:error, {:already_started, _pid}} ->
            Logger.info("🎰 [SLOTNIU_JACKPOT] ✅ SlotNiu Jackpot进程已存在")
            :ok

          {:error, reason} ->
            Logger.error("🎰 [SLOTNIU_JACKPOT] ❌ SlotNiu Jackpot进程启动失败: #{inspect(reason)}")
            :error
        end

      _pid ->
        Logger.info("🎰 [SLOTNIU_JACKPOT] ✅ SlotNiu Jackpot进程已运行")
        :ok
    end
  end

  # 获取SlotNiu Jackpot记录（使用统一的流水表查询）
  defp get_slotniu_jackpot_records(state) do
    Logger.info("🎰 [JACKPOT_RECORDS] 从流水表获取SlotNiu Jackpot记录")

    case Teen.GameSystem.JackpotManager.get_display_records("slotniu", 20) do
      {:ok, records} ->
        Logger.info("🎰 [JACKPOT_RECORDS] ✅ 成功获取流水表记录: #{length(records)}条")
        {:ok, records}

      {:error, reason} ->
        Logger.error("🎰 [JACKPOT_RECORDS] ❌ 获取流水表记录失败: #{inspect(reason)}")
        {:error, reason}
    end
  end

  # 添加Jackpot中奖记录到全局管理器
  # 参考前端FMWJackpotRecordLayer.ts的onUpdateRecordInfo方法期望的数据格式
  defp add_jackpot_record_to_global(
         player_id,
         player_name,
         niunum,
         bet_amount,
         win_amount,
         mult \\ 1
       ) do
    try do
      # 确保所有参数都是有效值
      safe_player_id =
        if is_integer(player_id) and player_id > 0, do: player_id, else: :rand.uniform(999_999)

      safe_player_name =
        if is_binary(player_name) and String.length(player_name) > 0,
          do: player_name,
          else: "玩家#{safe_player_id}"

      safe_niunum = if is_integer(niunum) and niunum >= 0, do: niunum, else: 0
      safe_bet_amount = if is_number(bet_amount) and bet_amount > 0, do: bet_amount, else: 0
      safe_win_amount = if is_number(win_amount) and win_amount > 0, do: win_amount, else: 0
      safe_mult = if is_number(mult) and mult > 0, do: mult, else: 1

      # 记录添加前的状态
      Logger.info("🎰 [JACKPOT_RECORD] 🔄 准备添加Jackpot记录")

      Logger.info(
        "🎰 [JACKPOT_RECORD] 📊 原始参数: player_id=#{player_id}, player_name=#{player_name}, niunum=#{niunum}"
      )

      Logger.info(
        "🎰 [JACKPOT_RECORD] 📊 原始参数: bet_amount=#{bet_amount}, win_amount=#{win_amount}, mult=#{mult}"
      )

      Logger.info(
        "🎰 [JACKPOT_RECORD] 📊 安全参数: player_id=#{safe_player_id}, player_name=#{safe_player_name}, niunum=#{safe_niunum}"
      )

      Logger.info(
        "🎰 [JACKPOT_RECORD] 📊 安全参数: bet_amount=#{safe_bet_amount}, win_amount=#{safe_win_amount}, mult=#{safe_mult}"
      )

      SlotNiuJackpot.add_jackpot_record(
        safe_player_id,
        safe_player_name,
        safe_niunum,
        safe_bet_amount,
        safe_win_amount,
        safe_mult
      )

      Logger.info("🎰 [JACKPOT_RECORD] ✅ 已成功添加到全局记录")
      Logger.info("🎰 [JACKPOT_RECORD] 🎉 新记录: #{safe_player_name} (ID: #{safe_player_id})")

      Logger.info(
        "🎰 [JACKPOT_RECORD] 🎉 详情: #{safe_niunum}牛, 下注₹#{safe_bet_amount}, 奖金₹#{safe_win_amount}, #{safe_mult}倍"
      )

      # 验证记录是否成功添加
      case SlotNiuJackpot.get_total_records() do
        {:ok, total} ->
          Logger.info("🎰 [JACKPOT_RECORD] 📈 当前全局记录总数: #{total}")

        _ ->
          Logger.warning("🎰 [JACKPOT_RECORD] ⚠️ 无法获取记录总数")
      end
    rescue
      error ->
        Logger.error("🎰 [JACKPOT_RECORD] ❌ 添加全局记录失败: #{inspect(error)}")

        Logger.error(
          "🎰 [JACKPOT_RECORD] ❌ 原始参数: player_id=#{inspect(player_id)}, player_name=#{inspect(player_name)}, niunum=#{inspect(niunum)}"
        )

        Logger.error(
          "🎰 [JACKPOT_RECORD] ❌ 原始参数: bet_amount=#{inspect(bet_amount)}, win_amount=#{inspect(win_amount)}, mult=#{inspect(mult)}"
        )

        Logger.error("🎰 [JACKPOT_RECORD] ❌ 错误堆栈: #{Exception.format_stacktrace(__STACKTRACE__)}")
    end
  end

  # 检查是否应该添加到Jackpot记录（大奖才记录）
  # 参考旧项目逻辑：只有真正的大奖才会被记录到Jackpot列表中
  defp should_add_to_jackpot_records?(niunum, win_amount) do
    cond do
      # 5牛大奖：奖金>=5万才记录（超级大奖）
      niunum >= 5 and win_amount >= 50000 ->
        Logger.info("🎰 [JACKPOT_RECORD] ✅ 5牛超级大奖符合记录条件 - 牛数: #{niunum}, 奖金: #{win_amount}")
        true

      # 4牛大奖：奖金>=2万才记录（大奖）
      niunum >= 4 and win_amount >= 20000 ->
        Logger.info("🎰 [JACKPOT_RECORD] ✅ 4牛大奖符合记录条件 - 牛数: #{niunum}, 奖金: #{win_amount}")
        true

      # 3牛中奖：奖金>=5千才记录（中奖）
      niunum >= 3 and win_amount >= 5000 ->
        Logger.info("🎰 [JACKPOT_RECORD] ✅ 3牛中奖符合记录条件 - 牛数: #{niunum}, 奖金: #{win_amount}")
        true

      # 转盘Jackpot：无论金额大小都记录（特殊情况）
      niunum == 0 and win_amount > 0 ->
        Logger.info("🎰 [JACKPOT_RECORD] ✅ 转盘Jackpot符合记录条件 - 奖金: #{win_amount}")
        true

      # 其他情况不记录
      true ->
        Logger.debug("🎰 [JACKPOT_RECORD] ❌ 不符合记录条件 - 牛数: #{niunum}, 奖金: #{win_amount}")
        false
    end
  end

  # 计算Jackpot倍数
  # 参考旧项目逻辑和前端FMWJackpotRecordLayer.ts显示的倍数格式
  defp calculate_jackpot_mult(niunum, win_amount) do
    cond do
      # 转盘Jackpot特殊处理
      niunum == 0 and win_amount > 0 ->
        # 转盘Jackpot根据奖金大小确定倍数
        cond do
          # 转盘超级大奖
          win_amount >= 100_000 -> 500
          # 转盘大奖
          win_amount >= 50000 -> 300
          # 转盘中奖
          win_amount >= 20000 -> 200
          # 转盘普通奖
          true -> 100
        end

      # 5牛大奖
      # 5牛超级大奖
      niunum >= 5 and win_amount >= 100_000 ->
        200

      # 5牛大奖
      niunum >= 5 and win_amount >= 50000 ->
        100

      # 4牛大奖
      # 4牛大奖
      niunum >= 4 and win_amount >= 50000 ->
        80

      # 4牛中奖
      niunum >= 4 and win_amount >= 20000 ->
        50

      # 3牛中奖
      # 3牛大奖
      niunum >= 3 and win_amount >= 20000 ->
        40

      # 3牛中奖
      niunum >= 3 and win_amount >= 5000 ->
        20

      # 其他情况（不应该被记录，但作为保险）
      # 普通倍数
      true ->
        10
    end
  end

  # 获取玩家当前积分（参考longhu实现）
  defp get_player_current_money(user_id) do
    if is_integer(user_id) and user_id < 0 do
      # 机器人虚拟积分
      1_000_000
    else
      Cypridina.Accounts.get_user_points(user_id)
    end
  end

  # 注释：已删除临时积分函数，现在使用longhu的积分管理方式
  # get_player_temp_money 和 update_player_temp_money 已被
  # get_player_points, add_player_points, subtract_player_points 替代

  # GenServer初始化
  # @impl true
  # def init(room_config) do
  #   # 创建初始状态
  #   state = %{
  #     id: room_config.id,
  #     config: room_config.config,
  #     status: room_config.status,
  #     players: %{},
  #     created_at: room_config.created_at,
  #     game_type: room_config.game_type,
  #     creator_id: room_config.creator_id
  #   }

  #   # 初始化游戏逻辑
  #   final_state = init_game_logic(state)

  #   {:ok, final_state}
  # end

  # 初始化游戏逻辑
  @impl true
  def init_game_logic(state) do
    Logger.info("🎰 [SLOTNIU] 初始化 SlotNiu 房间: #{state.id}")

    # 🎰 启动SlotNiu Jackpot进程（如果尚未启动）
    ensure_slotniu_jackpot_started()

    # 获取默认配置作为基础
    default_config = SlotNiuConfig.get_default_config()

    # 深度合并配置，确保所有配置项都有默认值兜底
    merged_config = deep_merge_config(default_config, state.config || %{})
    Logger.info("🎰 [SLOTNIU_CONFIG] 房间配置内容: #{inspect(merged_config, pretty: true)}")

    # 初始化奖池
    init_jackpots(state, merged_config)

    # 提取合并后的配置
    betting_config = merged_config.betting
    slot_config = merged_config.slot_config

    game_config = %{
      # 最小下注
      min_bet: betting_config.min_bet,
      # 最大下注
      max_bet: betting_config.max_bet,
      # 赔率配置 - 参考IndiaGameServer SlotNiuConfig.lua的AddBeiLv配置
      # IndiaGameServer: AddBeiLv = {1, 5, 10, 50, 100, 500, 1000}
      # 搭配TpMasterClient的BET_RATE_NUM(9)和期望的下注金额[1.8,9,18,90,180,900,1800]
      # 用于匹配前端
      odds_config: betting_config.odds_config,

      # 底分 - 根据用户反馈，前端显示下注18积分
      # 前端计算流程：
      # 1. 前端difen = 服务端difen / SCORE_RATE = 200 / 100 = 2
      # 2. 前端bets[0] = 1 × 9 × 2 = 18
      # 3. 前端betMoney = 18 × 100 = 1800
      # 4. 前端显示 = 1800 / 100 = 18积分
      difen: betting_config.difen,

      # 老虎机配置
      rows: slot_config.rows,
      cols: slot_config.cols,
      lines: slot_config.lines,
      max_lines: slot_config.max_lines
    }

    game_data = %{
      # 游戏配置 - 使用合并后的完整配置
      config: game_config,
      # 完整的合并配置 - 包含所有配置项
      full_config: merged_config,
      # 当前状态
      status: :waiting,
      # 玩家下注记录
      bets: %{},
      # 当前玩家下注倍率
      current_odds: %{},
      # 游戏结果
      results: [],
      # 当前回合
      current_round: 0,
      # 免费游戏状态
      free_games: %{},
      # 待发送的免费游戏
      pending_free_games: %{},
      # EXPANDING WILD游戏状态
      expanding_wild_games: %{},
      # 玩家的EXPANDING WILD状态 - 参考旧项目实现
      player_wild_states: %{},
      # 免费游戏保护期状态 - 20秒内阻止玩家点击开始游戏
      free_game_protection: %{},
      # 玩家最后游戏结果 - 用于转盘开始请求获取牛头数量
      player_last_results: %{},
      # PRIZE奖池金额 - 根据规则，游戏中转到牛会增加奖池（客户端期望的prize字段）
      prize_pool: 0,
      # Jackpot奖池金额 (allJackpotNum) - 从真实奖池系统动态获取
      jackpot_amount: get_jackpot_amount_safe(state),
      # Prize奖池金额 (font_prize) - 用于前端显示Prize奖池
      prize_amount: get_prize_amount_safe(),
      # 上次Jackpot金额（用于增量更新）
      last_jackpot_amount: get_jackpot_amount_safe(state),
      # 上次Prize金额（用于增量更新）
      last_prize_amount: get_prize_amount_safe()
    }

    # 启动奖池更新定时器
    schedule_prize_update()

    %{state | game_data: game_data}
  end

  # 定时更新奖池金额
  defp schedule_prize_update do
    # 每30秒更新一次奖池
    Process.send_after(self(), :update_prize_pools, 2_000)
  end

  # 玩家加入房间后的处理 - 适配RoomBase的2参数签名（参考longhu实现）
  @impl true
  def on_player_joined(state, player) do
    user_id = player.user_id
    numeric_id = player.numeric_id

    # 发送相关信息给新玩家
    send_player_list_to_user(state, user_id)
    send_room_info_to_user(state, user_id)
    send_game_config(state, user_id)
    broadcast_player_count_change(state)

    Logger.info("🎰 [SLOTNIU_JOIN] ========== 玩家加入完成 ==========")
    state
  end

  # 玩家重连加入房间 - 适配RoomBase的2参数签名（参考longhu实现）
  @impl true
  def on_player_rejoined(state, player) do
    user_id = player.user_id
    numeric_id = player.numeric_id

    Logger.info("🎰 [SLOTNIU_REJOIN] ========== 玩家重连加入 ==========")
    Logger.info("🎰 [SLOTNIU_REJOIN] 用户ID: #{user_id}")
    Logger.info("🎰 [SLOTNIU_REJOIN] 数字ID: #{numeric_id}")

    # 注意：RoomBase已经处理了玩家重连逻辑，我们只需要发送配置信息
    # 检查玩家积分（用于日志）
    current_points = get_player_points(state, numeric_id)
    Logger.info("🎰 [SLOTNIU_REJOIN] 重连玩家当前积分: #{current_points}")

    # 发送房间信息给重连玩家
    Logger.info("🎰 [SLOTNIU_REJOIN] 发送房间信息给重连玩家...")
    send_room_info_to_user(state, user_id)

    # 🎯 发送游戏配置给重连玩家 (前端通过onGameConfig接收)
    Logger.info("🎰 [SLOTNIU_REJOIN] 发送游戏配置给玩家...")
    send_game_config(state, user_id)

    Logger.info("🎰 [SLOTNIU_REJOIN] ========== 重连处理完成 ==========")
    state
  end

  # 玩家离开房间 - 适配RoomBase的2参数签名（参考longhu方式）
  @impl true
  def on_player_left(state, player) do
    user_id = player.user_id
    Logger.info("🎰 [SLOTNIU] 玩家离开: #{user_id}")

    # 注释：不再需要同步临时积分，因为使用longhu的积分管理方式
    # 积分变化已经实时同步到数据库

    # 广播玩家数量变化通知
    broadcast_player_count_change(state)

    state
  end

  # 游戏开始
  @impl true
  def on_game_start(state) do
    Logger.info("🎰 [SLOTNIU] 游戏开始: #{state.id}")
    state
  end

  # 处理游戏消息 - 适配RoomBase的3参数签名
  @impl true
  def handle_game_message(state, player, message) do
    user_id = player.user_id

    case message do
      # 处理MainID=5的SlotNiu协议消息 (XC: 子游戏服务器和客户端交互的协议)
      %{"mainId" => 5, "subId" => sub_id, "data" => data} ->
        handle_slotniu_protocol(state, user_id, sub_id, data)

      # 处理MainID=5的SlotNiu协议消息 (无data字段)
      %{"mainId" => 5, "subId" => sub_id} ->
        handle_slotniu_protocol(state, user_id, sub_id, %{})

      # 处理MainID=4的通用游戏协议 (带data字段)
      %{"mainId" => 4, "subId" => sub_id, "data" => data} ->
        handle_generic_game_protocol(state, user_id, sub_id, data)

      # 处理MainID=4的通用游戏协议 (无data字段)
      %{"mainId" => 4, "subId" => sub_id} ->
        handle_generic_game_protocol(state, user_id, sub_id, %{})

      # 兼容性处理
      %{"cmd" => "get_room_info"} ->
        Logger.info("🎰 [SLOTNIU_MSG] ✅ 匹配房间信息请求")
        send_room_info_to_user(state, user_id)
        state

      _ ->
        Logger.info("🎰 [SLOTNIU_MSG] ❌ 未知消息类型，无法处理")
        Logger.info("🎰 [SLOTNIU_MSG] 🔍 消息结构分析:")

        Logger.info(
          "🎰 [SLOTNIU_MSG]    - 类型: #{if is_map(message), do: "map", else: inspect(message.__struct__)}"
        )

        Logger.info(
          "🎰 [SLOTNIU_MSG]    - 键: #{if is_map(message), do: inspect(Map.keys(message)), else: "非map类型"}"
        )

        state
    end
  end

  # 处理通用游戏协议 (MainID=4)
  defp handle_generic_game_protocol(state, user_id, sub_id, data) do
    Logger.info("🎰 [SLOTNIU_GENERIC] 处理通用协议 - SubID: #{sub_id}, Data: #{inspect(data)}")

    case sub_id do
      # 可能的房间信息请求
      _ ->
        Logger.info("🎰 [SLOTNIU_GENERIC] 未处理的通用协议: #{sub_id}")
        # 发送房间信息作为默认响应
        send_room_info_to_user(state, user_id)
        state
    end
  end

  # 处理SlotNiu协议消息
  defp handle_slotniu_protocol(state, user_id, sub_id, data) do
    Logger.info("🎰 [SLOTNIU_PROTOCOL] ========== 处理SlotNiu协议 ==========")
    Logger.info("🎰 [SLOTNIU_PROTOCOL] 用户: #{user_id}")
    Logger.info("🎰 [SLOTNIU_PROTOCOL] SubID: #{sub_id}")
    Logger.info("🎰 [SLOTNIU_PROTOCOL] 数据: #{inspect(data, pretty: true)}")
    Logger.info("🎰 [SLOTNIU_PROTOCOL] 时间: #{DateTime.utc_now()}")

    case sub_id do
      # 游戏开始 (CS_SLOTNIU_GAMESTART_P)
      1000 ->
        Logger.info("🎰 [SLOTNIU_PROTOCOL] ✅ 处理游戏开始请求 (CS_SLOTNIU_GAMESTART_P)")
        handle_game_start_request(state, user_id, data)

      # 转盘开始 (CS_SLOTNIU_SPINSTART_P)
      1003 ->
        Logger.info("🎰 [SLOTNIU_PROTOCOL] ✅ 处理转盘开始请求 (CS_SLOTNIU_SPINSTART_P)")
        handle_spin_start_request(state, user_id, data)

      # Jackpot记录 (CS_SLOTNIU_JPLIST_P)
      1005 ->
        Logger.info("🎰 [SLOTNIU_PROTOCOL] ✅ 处理Jackpot记录请求 (CS_SLOTNIU_JPLIST_P)")
        handle_jackpot_list_request(state, user_id, data)

      # 其他未实现的协议
      _ ->
        Logger.warning("🎰 [SLOTNIU_PROTOCOL] ❌ 未实现的子协议: #{sub_id}")
        Logger.warning("🎰 [SLOTNIU_PROTOCOL] 📋 已支持的协议列表:")
        Logger.warning("🎰 [SLOTNIU_PROTOCOL]    - 1000: 游戏开始 (CS_SLOTNIU_GAMESTART_P)")
        Logger.warning("🎰 [SLOTNIU_PROTOCOL]    - 1003: 转盘开始 (CS_SLOTNIU_SPINSTART_P)")
        Logger.warning("🎰 [SLOTNIU_PROTOCOL]    - 1005: Jackpot记录 (CS_SLOTNIU_JPLIST_P)")
        Logger.warning("🎰 [SLOTNIU_PROTOCOL] ⚠️ 未实现的协议: #{sub_id}")
        # 不发送错误响应，直接返回状态
        state
    end
  end

  # 处理游戏开始请求
  defp handle_game_start_request(state, user_id, data) do
    Logger.info("🎰 [SLOTNIU] 处理游戏开始请求 - 用户: #{user_id}")

    # 🎯 检查免费游戏保护期 - 20秒内阻止玩家点击开始游戏
    if is_in_free_game_protection(state, user_id) do
      Logger.info("🎰 [FREE_GAME_PROTECTION] 玩家#{user_id}在免费游戏保护期内点击开始游戏，忽略请求")
      state
    else
      odds = Map.get(data, "odds", 9)

      # 调用内部游戏开始处理
      case handle_call({:start_game, user_id, odds}, nil, state) do
        {:reply, {:ok, result}, new_state} ->
          # 🔍 调试：检查发送给客户端的数据中是否包含wildtype和wildnum
          Logger.info("🎰 [PROTOCOL_DEBUG] ========== 发送协议调试 ==========")

          Logger.info(
            "🎰 [PROTOCOL_DEBUG] 发送给客户端的result中的wildtype: #{Map.get(result, "wildtype", "未找到")}"
          )

          Logger.info(
            "🎰 [PROTOCOL_DEBUG] 发送给客户端的result中的wildnum: #{Map.get(result, "wildnum", "未找到")}"
          )

          Logger.info("🎰 [PROTOCOL_DEBUG] 发送给客户端的完整result: #{inspect(result, pretty: true)}")

          # 发送游戏结果
          response = %{
            "mainId" => 5,
            # SC_SLOTNIU_GAMESTART_P
            "subId" => 1001,
            "data" => result
          }

          send_to_player_by_id(new_state, user_id, response)

          # 🎯 检查是否触发免费游戏，如果是则生成并发送免费游戏数据
          final_state =
            if Map.get(result, "freetimes", 0) > 0 do
              Logger.info(
                "🎰 [FREE_GAME_TRIGGER] 检测到免费游戏触发 - 用户: #{user_id}, 免费次数: #{result["freetimes"]}, 倍率: #{odds}"
              )

              handle_free_game_trigger(new_state, user_id, result["freetimes"], odds)
            else
              new_state
            end

          final_state

        {:reply, {:error, reason}, state} ->
          # 发送错误消息
          Logger.error("🎰 [SLOTNIU_GAME_START] ❌ 游戏开始失败: #{reason}")
          # 发送错误响应
          error_response = %{
            "mainId" => 5,
            "subId" => 1001,
            "data" => %{
              "code" => 1,
              "msg" => reason
            }
          }

          send_to_player_by_id(state, user_id, error_response)
          state
      end
    end
  end

  # 处理游戏开始请求 - 内部实现（参考longhu积分管理）
  @impl true
  def handle_call({:start_game, user_id, odds}, _from, state) do
    Logger.info("🎰 [SLOTNIU] 玩家 #{user_id} 开始游戏，倍率: #{odds}")

    # 🔧 添加防御性检查 - 确保game_data存在
    if is_nil(state.game_data) do
      Logger.error("❌ [SLOTNIU_ERROR] state.game_data为nil，无法处理游戏开始请求")
      {:reply, {:error, "游戏数据异常"}, state}
    else
      # 获取玩家的numeric_id
      {:ok, numeric_id} = get_numeric_id(user_id)

      # 验证玩家是否在房间中（使用numeric_id作为键）
      if not Map.has_key?(state.players, numeric_id) do
        {:reply, {:error, "玩家不在房间中"}, state}
      else
        # 🔧 添加防御性检查 - 确保config存在
        if is_nil(state.game_data.config) do
          Logger.error("❌ [SLOTNIU_ERROR] state.game_data.config为nil，无法验证倍率")
          {:reply, {:error, "游戏配置异常"}, state}
        else
          # 🎯 记录玩家使用的倍率索引，用于下次发送lastodds
          odds_index =
            case Enum.find_index(state.game_data.config.odds_config, fn x -> x == odds end) do
              # 如果找不到，默认为0
              nil -> 0
              index -> index
            end

          Logger.info("🎰 [SLOTNIU] 玩家倍率索引: #{odds_index}")

          # 验证倍率是否有效
          if odds not in state.game_data.config.odds_config do
            {:reply, {:error, "无效的下注倍率"}, state}
          else
            # 获取玩家当前积分（使用longhu的方式，使用numeric_id）
            current_points = get_player_points(state, numeric_id)

            # 🔧 修复下注金额计算 - 与前端计算保持一致
            # 前端计算流程：
            # 1. 前端difen = 服务端difen / SCORE_RATE = 20 / 100 = 0.2
            # 2. bets[i] = 倍率 × BET_RATE_NUM(9) × 前端difen = 1 × 9 × 0.2 = 1.8
            # 3. betMoney = bets[i] × SCORE_RATE(100) = 1.8 × 100 = 180
            # 所以服务端应该直接使用前端期望的下注金额
            # BET_RATE_NUM固定值，与前端保持一致
            bet_rate_num = 9
            # 前端difen = 服务端difen / SCORE_RATE
            frontend_difen = state.game_data.config.difen / 100
            # 最终下注金额
            bet_amount = trunc(odds * bet_rate_num * frontend_difen * 100)

            Logger.info("🎰 [SLOTNIU] 下注计算详情:")
            Logger.info("🎰 [SLOTNIU] - 倍率: #{odds}")
            Logger.info("🎰 [SLOTNIU] - 固定倍率(BET_RATE_NUM): #{bet_rate_num}")
            Logger.info("🎰 [SLOTNIU] - 底分: #{state.game_data.config.difen}")

            Logger.info(
              "🎰 [SLOTNIU] - 计算公式: #{odds} × #{bet_rate_num} × #{state.game_data.config.difen} = #{bet_amount}"
            )

            Logger.info("🎰 [SLOTNIU] - 前端显示: #{bet_amount / 100}积分")
            Logger.info("🎰 [SLOTNIU] 玩家当前积分: #{current_points}, 下注金额: #{bet_amount}")

            # 检查玩家余额（参考longhu的验证方式）
            if current_points < bet_amount do
              {:reply, {:error, "余额不足"}, state}
            else
              # 🎯 更新玩家的倍率索引记录
              updated_state = update_player_odds_index(state, numeric_id, odds_index)

              # 🔧 添加防御性检查 - 确保jackpot_state.game_data存在
              # 先扣除下注金额（参考longhu的做法，使用numeric_id）
              state_after_bet = subtract_player_points(updated_state, numeric_id, bet_amount)

              # 处理抽水和奖池贡献
              player = get_player_by_user_id(state_after_bet, user_id)
              rake_amount = cacl_rake_amount(state_after_bet, bet_amount)

              if rake_amount > 0 do
                game_rake(state_after_bet, player, rake_amount)
              end

              # 处理奖池贡献
              contribution_state =
                handle_jackpot_contribution(state_after_bet, player, bet_amount)

              # 生成游戏结果，传递完整配置和玩家EXPANDING WILD状态
              # 使用完整配置以确保游戏逻辑能访问所有必要的配置项
              game_config =
                Map.merge(contribution_state.game_data.full_config, %{
                  jackpot_amount: state.game_data.jackpot_amount,
                  prize_pool: state.game_data.prize_pool
                })

              # 🎯 获取玩家的EXPANDING WILD状态
              player_wild_state =
                Map.get(contribution_state.game_data.player_wild_states, user_id)

              Logger.info(
                "🎰 [GAME_RESULT] 玩家#{user_id}的EXPANDING WILD状态: #{inspect(player_wild_state)}"
              )

              game_result =
                SlotNiuLogic.generate_game_result(
                  odds,
                  current_points,
                  contribution_state.game_data.config.difen,
                  game_config,
                  player_wild_state
                )

              # 🎯 更新玩家的EXPANDING WILD状态（消耗一次使用次数）- 参考旧项目SubExpWildNum()
              updated_state_with_wild =
                if player_wild_state do
                  updated_wild_state = %{
                    player_wild_state
                    | wild_num: player_wild_state.wild_num - 1,
                      current_column: max(1, player_wild_state.current_column - 1)
                  }

                  # 如果次数用完，清除状态
                  final_wild_state =
                    if updated_wild_state.wild_num <= 0 do
                      Logger.info("🎰 [EXPANDING_WILD] 玩家#{user_id}的EXPANDING WILD已用完")
                      nil
                    else
                      Logger.info(
                        "🎰 [EXPANDING_WILD] 玩家#{user_id}的EXPANDING WILD剩余: #{updated_wild_state.wild_num}次, 当前列: #{updated_wild_state.current_column}"
                      )

                      updated_wild_state
                    end

                  %{
                    state
                    | game_data: %{
                        state.game_data
                        | player_wild_states:
                            if final_wild_state do
                              Map.put(
                                state.game_data.player_wild_states,
                                user_id,
                                final_wild_state
                              )
                            else
                              Map.delete(state.game_data.player_wild_states, user_id)
                            end
                      }
                  }
                else
                  state
                end

              # 计算奖金
              # 加回下注金额
              win_amount = game_result["changemoney"] + bet_amount

              Logger.info(
                "🎰 [SLOTNIU] 游戏结果 - 用户: #{user_id}, 下注: #{bet_amount}, 奖金: #{win_amount}"
              )

              # 🎯 更新PRIZE奖池 - 根据C++服务端逻辑
              niunum = game_result["niunum"]
              freetimes = game_result["freetimes"]
              difen = state.game_data.config.difen
              bet_multiplier = odds

              # 🎯 记录特殊功能互斥逻辑的执行情况
              if niunum >= 3 and freetimes > 0 do
                Logger.warn(
                  "⚠️ [SPECIAL_FEATURES] 检测到同时触发牛头(#{niunum})和免费游戏(#{freetimes})，这不应该发生！"
                )

                Logger.warn("⚠️ [SPECIAL_FEATURES] 游戏结果: #{inspect(game_result)}")
              else
                if niunum >= 3 do
                  Logger.info("🎰 [SPECIAL_FEATURES] 触发转盘：#{niunum}个牛头，免费游戏: #{freetimes}")
                else
                  if freetimes > 0 do
                    Logger.info("🎰 [SPECIAL_FEATURES] 触发免费游戏：#{freetimes}次，牛头数量: #{niunum}")
                  end
                end
              end

              # 先检查是否使用PRIZE奖池（牛头数量>=3时）
              {prize_used, remaining_pool} =
                SlotNiuLogic.use_prize_pool(niunum, state.game_data.prize_pool)

              # 然后更新PRIZE奖池（牛头数量<3时收集）
              {new_prize_pool, pool_increase} =
                SlotNiuLogic.update_prize_pool(niunum, difen, bet_multiplier, remaining_pool)

              # 🎯 计算最终中奖金额（包含PRIZE奖池）
              # 参考C++代码：当牛头数量>=3时，PRIZE奖池金额会加到中奖金额中
              final_win_amount = win_amount + prize_used

              # 🎯 获取动画类型（基于最终中奖金额）
              win_multiplier =
                if final_win_amount > 0 and bet_amount > 0 do
                  trunc(final_win_amount / bet_amount)
                else
                  0
                end

              animation_type = SlotNiuLogic.get_animation_type(win_multiplier, false)

              # 将动画类型和PRIZE奖池信息添加到游戏结果
              enhanced_game_result =
                Map.merge(game_result, %{
                  "animation_type" => animation_type,
                  "prize_pool_increase" => pool_increase,
                  "new_prize_pool" => new_prize_pool,
                  # PRIZE奖池使用金额
                  "prize_used" => prize_used,
                  # 包含PRIZE奖池的最终中奖金额
                  "final_win_amount" => final_win_amount
                })

              Logger.info("🎰 [SLOTNIU] 中奖计算详情:")
              Logger.info("🎰 [SLOTNIU] - 基础中奖: #{win_amount}")
              Logger.info("🎰 [SLOTNIU] - PRIZE奖池使用: #{prize_used}")
              Logger.info("🎰 [SLOTNIU] - 最终中奖: #{final_win_amount}")

              # 🎯 问题二修复：处理玩家中奖后的积分增加
              # 如果有奖金，添加到玩家积分（使用numeric_id）
              # 🚨 重要：使用updated_state_with_wild而不是state_after_bet，确保EXPANDING WILD状态被保留
              final_state =
                if final_win_amount > 0 do
                  Logger.info("🎰 [SLOTNIU] 玩家中奖，增加积分: #{final_win_amount}")
                  add_player_points(updated_state_with_wild, numeric_id, final_win_amount)
                else
                  Logger.info("🎰 [SLOTNIU] 玩家未中奖，无积分增加")
                  updated_state_with_wild
                end

              # 更新状态中的PRIZE奖池
              final_state = %{
                final_state
                | game_data: %{final_state.game_data | prize_pool: new_prize_pool}
              }

              # 🎰 检查是否需要添加到Jackpot记录（参考旧项目逻辑）
              # 只有真正的大奖才会被记录到全局Jackpot列表中，供所有玩家查看
              jackpot_state =
                if game_result["changemoney"] > 0 do
                  Logger.info("🎰 [JACKPOT_RECORD] 🔍 检查是否需要记录中奖数据")

                  # 获取玩家信息（使用numeric_id作为键）
                  player_data = Map.get(final_state.players, numeric_id)

                  player_name =
                    if player_data do
                      nickname = Map.get(player_data.user, :usesrname, "玩家#{numeric_id}")
                      Logger.info("🎰 [JACKPOT_RECORD] 👤 玩家信息: #{nickname} (ID: #{numeric_id})")
                      nickname
                    else
                      default_name = "玩家#{numeric_id}"
                      Logger.warning("🎰 [JACKPOT_RECORD] ⚠️ 未找到玩家数据，使用默认名称: #{default_name}")
                      default_name
                    end

                  headid = Map.get(player_data.user, :avatar_id, 1)
                  wxheadurl = Map.get(player_data.user, :avatar_url, "")

                  # 从游戏结果中获取牛的个数和中奖金额
                  niunum = Map.get(game_result, "niunum", 0)
                  # 中奖金额
                  win_amount = game_result["changemoney"]
                  mult = calculate_jackpot_mult(niunum, win_amount)

                  Logger.info("🎰 [JACKPOT_RECORD] 📊 游戏结果分析:")
                  Logger.info("🎰 [JACKPOT_RECORD] 📊 - 牛数: #{niunum}")
                  Logger.info("🎰 [JACKPOT_RECORD] 📊 - 中奖金额: #{win_amount}")
                  Logger.info("🎰 [JACKPOT_RECORD] 📊 - 下注金额: #{bet_amount}")
                  Logger.info("🎰 [JACKPOT_RECORD] 📊 - 计算倍数: #{mult}")

                  # 检查是否应该添加到Jackpot记录
                  if should_add_to_jackpot_records?(niunum, win_amount) do
                    Logger.info("🎰 [JACKPOT_RECORD] 🎉 符合记录条件，添加到全局Jackpot记录")

                    # add_jackpot_record_to_global(
                    #   numeric_id,
                    #   player_name,
                    #   niunum,
                    #   bet_amount,
                    #   win_amount,
                    #   mult
                    # )
                    award_data = %{
                      "playerid" => numeric_id,
                      "playername" => player_name,
                      "winscore" => win_amount,
                      "jackpotlevel" => mult,
                      "niunum" => niunum,
                      "betamount" => bet_amount,
                      "name" => player_name,
                      "headid" => headid,
                      "wxheadurl" => wxheadurl
                    }

                    # # 发送Jackpot中奖通知给房间内所有玩家（1008协议）
                    # jackpot_award_message = %{
                    #   "mainId" => 5,
                    #   # SC_SLOTNIU_JPAWARD_P
                    #   "subId" => 1008,
                    #   "data" => %{
                    #     "playerid" => numeric_id,
                    #     "playername" => player_name,
                    #     "winscore" => win_amount,
                    #     "jackpotlevel" => mult,
                    #     "niunum" => niunum,
                    #     "betamount" => bet_amount
                    #   }
                    # }
                    case broadcast_jackpot_to_all_room(:slotniu, 1008, award_data) do
                      {:ok, result} ->
                        Logger.info(
                          "🎉 [GLOBAL_JACKPOT] 全局广播成功 - 成功: #{result.success}, 总计: #{result.total} 房间"
                        )

                      {:error, reason} ->
                        Logger.error("❌ [GLOBAL_JACKPOT] 全局广播失败: #{inspect(reason)}")
                    end

                    Logger.info(
                      "🎉 [SLOTCAT_JACKPOT_AWARD] 广播Jackpot中奖通知 - 用户: #{numeric_id}, 奖金: #{win_amount}, Jackpot数量: #{niunum}"
                    )

                    # broadcast_to_room(final_state, jackpot_award_message)
                    Logger.info("🎰 [JACKPOT_RECORD] 📢 已广播Jackpot中奖通知 (1008协议)")
                  else
                    Logger.debug("🎰 [JACKPOT_RECORD] ℹ️ 不符合记录条件，跳过记录")
                  end

                  final_state
                else
                  Logger.debug("🎰 [JACKPOT_RECORD] ℹ️ 无中奖金额，跳过Jackpot记录检查")
                  final_state
                end

              # 🔧 添加防御性检查 - 确保jackpot_state.game_data存在
              if is_nil(jackpot_state.game_data) do
                Logger.error("❌ [SLOTNIU_ERROR] jackpot_state.game_data为nil，无法更新房间状态")
                {:reply, {:error, "游戏状态异常"}, state}
              else
                # 🎯 保存玩家最后游戏结果 - 用于转盘开始请求获取牛头数量
                {:ok, numeric_id} = get_numeric_id(user_id)
                niunum = Map.get(game_result, "niunum", 0)

                player_last_result = %{
                  niunum: niunum,
                  bet_amount: bet_amount,
                  win_amount: Map.get(game_result, "changemoney", 0),
                  timestamp: DateTime.utc_now()
                }

                # 更新房间状态 - 🚨 重要：确保EXPANDING WILD状态被保留
                new_game_data = %{
                  jackpot_state.game_data
                  | current_round: jackpot_state.game_data.current_round + 1,
                    current_odds: Map.put(jackpot_state.game_data.current_odds, user_id, odds),
                    # 🎯 关键修复：确保player_wild_states从final_state中获取，而不是从jackpot_state
                    player_wild_states: final_state.game_data.player_wild_states,
                    # 🎯 保存玩家最后游戏结果
                    player_last_results:
                      Map.put(
                        jackpot_state.game_data.player_last_results,
                        numeric_id,
                        player_last_result
                      )
                }

                new_state = %{jackpot_state | game_data: new_game_data}

                # 🎯 调试：验证EXPANDING WILD状态是否正确保存
                Logger.info("🎰 [EXPANDING_WILD_DEBUG] ========== 状态保存验证 ==========")

                Logger.info(
                  "🎰 [EXPANDING_WILD_DEBUG] final_state中的player_wild_states: #{inspect(final_state.game_data.player_wild_states)}"
                )

                Logger.info(
                  "🎰 [EXPANDING_WILD_DEBUG] new_state中的player_wild_states: #{inspect(new_state.game_data.player_wild_states)}"
                )

                Logger.info(
                  "🎰 [EXPANDING_WILD_DEBUG] 玩家#{user_id}的EXPANDING WILD状态: #{inspect(Map.get(new_state.game_data.player_wild_states, user_id))}"
                )

                # 获取玩家最新积分（使用numeric_id）
                final_points = get_player_points(new_state, numeric_id)

                # 🎯 获取玩家当前PRIZE奖池金额（从房间状态获取）
                # 根据客户端逻辑，prize字段应该反映玩家当前的prize数量
                current_player_prize = new_state.game_data.prize_pool

                Logger.info("🎰 [PRIZE_POOL] PRIZE奖池详情:")
                Logger.info("🎰 [PRIZE_POOL] - 牛数量: #{niunum}")
                Logger.info("🎰 [PRIZE_POOL] - 下注倍率: #{bet_multiplier}")
                Logger.info("🎰 [PRIZE_POOL] - 奖池增加: #{pool_increase}")
                Logger.info("🎰 [PRIZE_POOL] - 当前奖池: #{current_player_prize}")

                # 🔧 修复问题二：游戏结果后积分更新
                # 前端在游戏开始时扣除了积分，但游戏结果返回后没有更新积分
                # 需要在游戏结果中包含正确的积分信息，让前端能够更新playScoreMoney

                # 🔧 修复问题二：游戏结果后积分更新
                # 参考slot777的处理方法，确保前端能够正确更新myselfMoney
                # 前端在doFruitMachineEnd中会设置：playScoreMoney = myselfMoney
                # 所以需要确保myselfMoney被正确更新

                # 🎯 修正PRIZE奖池协议数据 - 确保前端收到正确的信息
                # 根据旧项目分析，前端期望的协议格式：
                # - prize: 当前PRIZE奖池金额（更新后的）
                # - changemoney: 玩家的输赢分（包含PRIZE奖池使用）
                # - winmoney: 中奖金额（包含PRIZE奖池使用）
                #
                # 🚨 前端显示流程问题：
                # 当前前端在收到1001协议后立即更新金币（FMWGameCore.ts第836-837行）
                # 正确流程应该是：先播放动画，动画结束后再更新金币
                # 这需要前端修改，将金币更新逻辑移到动画结束回调中
                enhanced_result =
                  enhanced_game_result
                  # 发送原始积分
                  |> Map.put("current_money", final_points)
                  # 添加pmoney字段（前端期望的积分字段）
                  |> Map.put("pmoney", final_points)
                  |> Map.put("playerid", numeric_id)
                  # 🎯 修正：发送更新后的PRIZE奖池金额
                  |> Map.put("prize", new_prize_pool)
                  # 🎯 修正：净收益（包含PRIZE奖池）
                  |> Map.put("changemoney", final_win_amount - bet_amount)
                  # 🎯 修正：总中奖金额（包含PRIZE奖池）
                  |> Map.put("winmoney", final_win_amount)

                Logger.info("🎰 [SLOTNIU] 游戏结果积分: #{final_points}")

                Logger.info(
                  "🎰 [SLOTNIU] 发送字段: current_money=#{final_points}, pmoney=#{final_points}"
                )

                # 🔍 调试：检查wildtype和wildnum是否在最终结果中
                Logger.info("🎰 [WILD_DEBUG] ========== EXPANDING WILD参数调试 ==========")

                Logger.info(
                  "🎰 [WILD_DEBUG] enhanced_game_result中的wildtype: #{Map.get(enhanced_game_result, "wildtype", "未找到")}"
                )

                Logger.info(
                  "🎰 [WILD_DEBUG] enhanced_game_result中的wildnum: #{Map.get(enhanced_game_result, "wildnum", "未找到")}"
                )

                Logger.info(
                  "🎰 [WILD_DEBUG] enhanced_result中的wildtype: #{Map.get(enhanced_result, "wildtype", "未找到")}"
                )

                Logger.info(
                  "🎰 [WILD_DEBUG] enhanced_result中的wildnum: #{Map.get(enhanced_result, "wildnum", "未找到")}"
                )

                Logger.info(
                  "🎰 [WILD_DEBUG] enhanced_result完整内容: #{inspect(enhanced_result, pretty: true)}"
                )

                # 🔧 关键修复：发送积分更新消息，确保前端myselfMoney被正确更新
                # 使用正确的协议触发前端updatePlayerMoney函数
                # MainID=4 (Game), SubID=8 (SC_ROOM_RESET_COIN_P)
                # updatePlayerMoney只是更新数据，前端会在需要显示时使用更新后的myselfMoney
                money_update_message = %{
                  # MainProto.Game
                  "mainId" => 4,
                  # Game.SC_ROOM_RESET_COIN_P - 这会触发前端updatePlayerMoney
                  "subId" => 8,
                  "data" => %{
                    "playerid" => numeric_id,
                    # 这个字段会更新前端myselfMoney
                    "coin" => final_points
                  }
                }

                # 发送积分更新消息
                send_to_player_by_id(new_state, user_id, money_update_message)
                Logger.info("🎰 [SLOTNIU] 发送积分更新消息 (MainID=4, SubID=8): #{final_points}")

                # 🎯 发送PRIZE奖池更新协议（如果奖池有变化）
                if pool_increase > 0 do
                  # send_prize_update_to_player(new_state, user_id, new_state.game_data.prize_pool)
                end

                {:reply, {:ok, enhanced_result}, new_state}
              end
            end
          end
        end
      end
    end
  end

  # 处理转盘开始请求
  defp handle_spin_start_request(state, user_id, data) do
    Logger.info("🎰 [SLOTNIU] 处理转盘开始请求 - 用户: #{user_id}")
    Logger.info("🎰 [SLOTNIU] 转盘请求数据: #{inspect(data)}")

    # 🔧 添加防御性检查 - 确保game_data和config存在
    if is_nil(state.game_data) or is_nil(state.game_data.config) do
      Logger.error("❌ [SLOTNIU_ERROR] state.game_data或config为nil，无法处理转盘请求")
      state
    else
      # 🎯 从玩家最后游戏结果中获取牛头数量
      {:ok, numeric_id} = get_numeric_id(user_id)

      bull_count =
        case Map.get(state.game_data.player_last_results, numeric_id) do
          %{niunum: niunum} when is_integer(niunum) and niunum >= 3 ->
            Logger.info("🎰 [SLOTNIU] 从玩家最后游戏结果获取牛头数量: #{niunum}")
            niunum

          _ ->
            # 如果没有找到玩家最后游戏结果，尝试从客户端请求数据中获取
            case data do
              %{"bullcount" => count} when is_integer(count) -> count
              %{"niunum" => count} when is_integer(count) -> count
              # 默认值：触发转盘的最小牛头数
              _ -> 3
            end
        end

      Logger.info("🎰 [SLOTNIU] 转盘触发牛头数量: #{bull_count}")

      Logger.info(
        "🎰 [SLOTNIU] 玩家#{numeric_id}最后游戏结果: #{inspect(Map.get(state.game_data.player_last_results, numeric_id))}"
      )

      # 构建转盘配置，包含当前Jackpot金额和牛头数量
      # 使用完整配置以确保转盘逻辑能访问所有必要的配置项
      turntable_config =
        Map.merge(state.game_data.full_config, %{
          jackpot_amount: state.game_data.jackpot_amount,
          bull_count: bull_count
        })

      # 生成转盘结果
      turntable_result = SlotNiuLogic.generate_turntable_result(turntable_config)

      Logger.info("🎰 [SLOTNIU] 转盘结果生成完成:")
      Logger.info("🎰 [SLOTNIU] - luckyspinid: #{turntable_result["luckyspinid"]}")
      Logger.info("🎰 [SLOTNIU] - 奖励类型: #{turntable_result["type"]}")
      Logger.info("🎰 [SLOTNIU] - 奖励等级: #{turntable_result["value"]}")

      # 🎯 不在这里发送转盘结果，而是在处理具体奖励时发送
      # 这样可以确保winmoney字段正确设置

      # 🎯 处理转盘奖励，传递牛头数量信息
      updated_state =
        case turntable_result["type"] do
          "jackpot" ->
            # Jackpot奖励：更新玩家积分和记录
            handle_jackpot_win_with_bull_count(state, user_id, turntable_result, bull_count)

          "free_spins" ->
            # 免费游戏奖励：生成并发送免费游戏数据
            # 🎯 修复：获取玩家当前倍率并传入
            {:ok, numeric_id} = get_numeric_id(user_id)
            player_odds = get_player_last_odds(state, numeric_id)
            handle_free_spins_win(state, user_id, turntable_result, player_odds)

          "wild_single" ->
            # EXPANDING WILD奖励：启动EXPANDING WILD游戏
            handle_expanding_wild_win(state, user_id, turntable_result)

          "wild_double" ->
            # 双线Wild奖励：启动EXPANDING WILD游戏（暂时与单线相同）
            handle_expanding_wild_win(state, user_id, turntable_result)

          _ ->
            state
        end

      updated_state
    end
  end

  # 处理Jackpot中奖（带牛头数量参数）
  defp handle_jackpot_win_with_bull_count(state, user_id, turntable_result, bull_count) do
    jackpot_level = turntable_result["jackpotlevel"]
    {:ok, numeric_id} = get_numeric_id(user_id)

    # 🎯 获取下注金额 - 从玩家最后游戏结果中获取实际下注金额
    bet_amount =
      case Map.get(state.game_data.player_last_results, numeric_id) do
        %{bet_amount: amount} when is_number(amount) ->
          Logger.info("🎰 [JACKPOT_WIN] 从玩家最后游戏结果获取下注金额: #{amount}")
          amount

        _ ->
          # 如果没有找到，使用计算方式
          player_last_odds = get_player_last_odds(state, numeric_id)
          difen = state.game_data.config.difen
          # 前端difen = 服务端difen / SCORE_RATE
          frontend_difen = difen / 100
          calculated_amount = trunc(player_last_odds * 9 * frontend_difen * 100)

          Logger.info(
            "🎰 [JACKPOT_WIN] 计算得出下注金额: #{calculated_amount} (倍率: #{player_last_odds}, 底分: #{difen})"
          )

          calculated_amount
      end

    # 🎯 使用真实奖池系统 - 获取实时奖池金额
    real_jackpot_amount = get_jackpot_amount_safe(state)

    # 第一步：计算转盘显示的Jackpot金额（luckyjackpot）
    lucky_jackpot =
      SlotNiuLogic.calculate_lucky_jackpot(real_jackpot_amount, bull_count, bet_amount)

    # 第二步：根据转盘结果计算最终奖励
    win_amount = SlotNiuLogic.calculate_jackpot_final_reward(lucky_jackpot, jackpot_level)

    Logger.info("🎰 [JACKPOT_WIN] 玩家#{user_id}中Jackpot#{jackpot_level}等奖")
    Logger.info("🎰 [JACKPOT_WIN] - 牛头数量: #{bull_count}")
    Logger.info("🎰 [JACKPOT_WIN] - 下注金额: #{bet_amount}")
    Logger.info("🎰 [JACKPOT_WIN] - 实时奖池: #{real_jackpot_amount}")
    Logger.info("🎰 [JACKPOT_WIN] - Jackpot等级: #{jackpot_level}")
    Logger.info("🎰 [JACKPOT_WIN] - 奖励金额: #{win_amount}")

    # 获取玩家信息用于记录
    player_data = Map.get(state.players, numeric_id)

    player_name =
      if player_data do
        Map.get(player_data.user, :username, "玩家#{numeric_id}")
      else
        "玩家#{numeric_id}"
      end

    headid =
      if player_data do
        Map.get(player_data.user, :avatar_id, 1)
      else
        1
      end

    avatar_url =
      if player_data do
        Map.get(player_data.user, :avatar_url, "")
      else
        ""
      end

    # 计算Jackpot倍数（根据下注金额和牛数计算）
    jackpot_mult = calculate_jackpot_multiplier_from_config(bet_amount, bull_count)

    # 使用真实奖池系统处理中奖
    {updated_state, final_win_amount} =
      case Cypridina.Ledger.jackpot_win(user_id, "41", "main", win_amount,
             description: "SlotNiu Jackpot中奖 - 等级#{jackpot_level}",
             metadata: %{
               "game_type" => "slotniu",
               "user_id" => user_id,
               "player_id" => user_id,
               "player_name" => player_name,
               "avatar_id" => headid,
               "avatar_url" => avatar_url,
               "win_amount" => win_amount,
               "bet_amount" => bet_amount,
               "jackpot_level" => jackpot_level,
               "game_specific_data" => %{
                 "bull_count" => bull_count,
                 "multiplier" => jackpot_mult,
                 "niunum" => bull_count,
                 "bet" => bet_amount,
                 "mult" => jackpot_mult
               }
             }
           ) do
        {:ok, _transfer} ->
          Logger.info("💰 [SLOTNIU] Jackpot中奖支付成功: 玩家#{user_id}, 金额#{win_amount}")
          # 注意：不需要手动更新玩家余额，Ledger.jackpot_win 已经处理了转账
          {state, win_amount}

        {:error, reason} ->
          Logger.error("❌ [SLOTNIU] Jackpot中奖支付失败: #{inspect(reason)}")
          # 支付失败时，不给予奖金
          {state, 0}
      end

    # 获取更新后的积分
    new_points = get_player_points(updated_state, numeric_id)
    Logger.info("🎰 [JACKPOT_WIN] ✅ 玩家积分更新成功: #{new_points}")

    # 获取玩家信息
    player_data = Map.get(updated_state.players, numeric_id)
    headid = Map.get(player_data.user, :avatar_id, 1)

    player_name =
      if player_data do
        Map.get(player_data.user, :username, "玩家#{numeric_id}")
      else
        "玩家#{numeric_id}"
      end

    # 只有实际获得奖金时才发送协议和记录
    if final_win_amount > 0 do
      # 🎯 发送转盘结果协议 - 参考IndiaGameServer实现
      # 包含winmoney字段，供前端FMWTurtableLayer.ts的playJackpotAnim使用
      turntable_response = %{
        "mainId" => 5,
        # SC_SLOTNIU_SPINSTART_P
        "subId" => 1004,
        "data" => %{
          "luckyspinid" => turntable_result["luckyspinid"],
          # 🎯 关键：前端playJackpotAnim需要的jackpotNum参数
          "winmoney" => final_win_amount,
          # 实际获得的金额
          "changemoney" => final_win_amount
        }
      }

      send_to_player_by_id(updated_state, user_id, turntable_response)

      # 🎰 转盘Jackpot记录逻辑 - 参考旧项目实现
      # 转盘Jackpot中奖都应该被记录，因为这是特殊的大奖事件
      Logger.info("🎰 [TURNTABLE_JACKPOT] 🎉 转盘Jackpot中奖，添加到全局记录")
      Logger.info("🎰 [TURNTABLE_JACKPOT] 📊 玩家: #{player_name} (ID: #{numeric_id})")

      Logger.info(
        "🎰 [TURNTABLE_JACKPOT] 📊 牛头数: #{bull_count}, 下注: #{bet_amount}, 奖金: #{final_win_amount}, 等级: #{jackpot_level}"
      )

      # # 转盘Jackpot使用特殊的niunum值来标识（使用bull_count作为niunum）
      # # 这样前端可以正确显示转盘Jackpot的类型
      # add_jackpot_record_to_global(
      #   numeric_id,
      #   player_name,
      #   bull_count,
      #   bet_amount,
      #   final_win_amount,
      #   jackpot_level
      # )
    else
      Logger.warning("🎰 [TURNTABLE_JACKPOT] ❌ Jackpot支付失败，不发送协议和记录")
    end

    # 发送Jackpot中奖通知给房间内所有玩家（1008协议）
    # 参考前端FMWGameCore.ts的onOhterPalyerJackpot方法期望的数据格式
    jackpot_award_message = %{
      "playerid" => numeric_id,
      "playername" => player_name,
      "winscore" => win_amount,
      "jackpotlevel" => jackpot_level,
      "bullcount" => bull_count,
      "betamount" => bet_amount,
      # 添加前端期望的字段
      "name" => player_name,
      # 默认头像ID
      "headid" => headid,
      # 自定义头像URL
      "wxheadurl" => ""
    }

    case broadcast_jackpot_to_all_room(:slotniu, 1008, jackpot_award_message) do
      {:ok, result} ->
        Logger.info("🎉 [GLOBAL_JACKPOT] 全局广播成功 - 成功: #{result.success}, 总计: #{result.total} 房间")

      {:error, reason} ->
        Logger.error("❌ [GLOBAL_JACKPOT] 全局广播失败: #{inspect(reason)}")
    end

    Logger.info(
      "🎉 [SLOTCAT_JACKPOT_AWARD] 广播Jackpot中奖通知 - 用户: #{numeric_id}, 奖金: #{win_amount}, Jackpot数量: #{bull_count}"
    )

    # broadcast_to_room(updated_state, jackpot_award_message)
    Logger.info("🎰 [TURNTABLE_JACKPOT] 📢 已广播转盘Jackpot中奖通知 (1008协议)")

    # 🔧 发送积分更新协议给玩家 (参考slot777实现)
    money_update_data = %{
      "playerid" => numeric_id,
      "coin" => new_points
    }

    response_money_update = %{
      # MainProto.Game
      "mainId" => 4,
      # Game.SC_ROOM_RESET_COIN_P
      "subId" => 8,
      "data" => money_update_data
    }

    Logger.info(
      "💰 [MONEY_UPDATE] 发送Jackpot中奖后积分更新 - 用户: #{user_id}, numeric_id: #{numeric_id}, 积分: #{new_points}"
    )

    send_to_player_by_id(updated_state, user_id, response_money_update)

    updated_state
  end

  # 处理Jackpot中奖（兼容旧版本，使用默认牛头数量）
  defp handle_jackpot_win(state, user_id, turntable_result) do
    handle_jackpot_win_with_bull_count(state, user_id, turntable_result, 3)
  end

  # 检查玩家是否在免费游戏保护期内
  defp is_in_free_game_protection(state, user_id) do
    case Map.get(state.game_data.free_game_protection, user_id) do
      nil ->
        false

      protection_end_time ->
        current_time = System.system_time(:millisecond)

        if current_time < protection_end_time do
          true
        else
          # 保护期已过，清除记录
          false
        end
    end
  end

  # 设置免费游戏保护期（20秒）
  defp set_free_game_protection(state, user_id) do
    current_time = System.system_time(:millisecond)
    # 20秒后结束保护期
    protection_end_time = current_time + 20_000

    Logger.info("🎰 [FREE_GAME_PROTECTION] 为玩家#{user_id}设置免费游戏保护期，结束时间: #{protection_end_time}")

    updated_protection =
      Map.put(state.game_data.free_game_protection, user_id, protection_end_time)

    %{state | game_data: %{state.game_data | free_game_protection: updated_protection}}
  end

  # 清除免费游戏保护期
  defp clear_free_game_protection(state, user_id) do
    Logger.info("🎰 [FREE_GAME_PROTECTION] 清除玩家#{user_id}的免费游戏保护期")
    updated_protection = Map.delete(state.game_data.free_game_protection, user_id)
    %{state | game_data: %{state.game_data | free_game_protection: updated_protection}}
  end

  # 处理免费游戏触发（从普通游戏结果中触发）
  defp handle_free_game_trigger(state, user_id, free_times, player_odds \\ 1) do
    Logger.info("🎰 [FREE_GAME_TRIGGER] 玩家#{user_id}触发免费游戏，次数: #{free_times}, 倍率: #{player_odds}")

    # 🎯 检查免费次数是否有效
    if free_times <= 0 do
      Logger.info("🎰 [FREE_GAME_TRIGGER] 免费次数为0，跳过免费游戏处理")
      state
    else
      # 🎯 设置免费游戏保护期 - 20秒内阻止玩家点击开始游戏
      state_with_protection = set_free_game_protection(state, user_id)

      # 🎯 修复：生成免费游戏数据时传入玩家倍率和用户ID，并获取更新后的PRIZE奖池
      {free_games_data, updated_prize_pool} =
        generate_free_games_data(state_with_protection, free_times, player_odds, user_id)

      # 🎯 更新房间状态中的PRIZE奖池
      state_with_updated_prize = %{
        state_with_protection
        | game_data: %{state_with_protection.game_data | prize_pool: updated_prize_pool}
      }

      # 🎯 验证免费游戏数据的完整性
      if map_size(free_games_data) == 0 do
        Logger.error("🎰 [FREE_GAME_TRIGGER] ❌ 免费游戏数据生成失败，数据为空")
        state_with_updated_prize
      else
        # 继续处理免费游戏逻辑
        handle_free_spins_processing(
          state_with_updated_prize,
          user_id,
          free_times,
          free_games_data
        )
      end
    end
  end

  # 处理免费游戏中奖（从转盘触发）
  defp handle_free_spins_win(state, user_id, turntable_result, player_odds \\ 1) do
    free_times = turntable_result["freetimes"]
    Logger.info("🎰 [FREE_SPINS_WIN] 玩家#{user_id}中免费游戏，次数: #{free_times}, 倍率: #{player_odds}")

    # 🎯 发送转盘结果协议 - 免费游戏中奖
    # 免费游戏中奖时winmoney为0（因为是免费游戏，不是直接金钱奖励）
    turntable_response = %{
      "mainId" => 5,
      # SC_SLOTNIU_SPINSTART_P
      "subId" => 1004,
      "data" => %{
        "luckyspinid" => turntable_result["luckyspinid"],
        # 免费游戏中奖时为0
        "winmoney" => 0,
        # 免费次数
        "freetimes" => free_times,
        # 免费游戏不直接给钱
        "changemoney" => 0
      }
    }

    send_to_player_by_id(state, user_id, turntable_response)

    # 🎯 检查免费次数是否有效
    if free_times <= 0 do
      Logger.info("🎰 [FREE_SPINS_WIN] 免费次数为0，跳过免费游戏处理")
      state
    else
      # 🎯 设置免费游戏保护期 - 20秒内阻止玩家点击开始游戏
      state_with_protection = set_free_game_protection(state, user_id)

      # 🎯 修复：生成免费游戏数据时传入玩家倍率和用户ID，并获取更新后的PRIZE奖池
      {free_games_data, updated_prize_pool} =
        generate_free_games_data(state_with_protection, free_times, player_odds, user_id)

      # 🎯 更新房间状态中的PRIZE奖池
      state_with_updated_prize = %{
        state_with_protection
        | game_data: %{state_with_protection.game_data | prize_pool: updated_prize_pool}
      }

      # 🎯 验证免费游戏数据的完整性
      if map_size(free_games_data) == 0 do
        Logger.error("🎰 [FREE_SPINS_WIN] ❌ 免费游戏数据生成失败，数据为空")
        state_with_updated_prize
      else
        # 继续处理免费游戏逻辑
        handle_free_spins_processing(
          state_with_updated_prize,
          user_id,
          free_times,
          free_games_data
        )
      end
    end
  end

  # 处理免费游戏的具体逻辑
  defp handle_free_spins_processing(state, user_id, free_times, free_games_data) do
    # 🎯 问题二修复：免费游戏的金额需要加到用户余额上
    # 计算免费游戏总奖金并更新玩家积分
    total_free_win =
      free_games_data
      |> Map.values()
      |> Enum.reduce(0, fn game_result, acc ->
        win_money = Map.get(game_result, "winmoney", 0)
        acc + win_money
      end)

    Logger.info("🎰 [FREE_SPINS_WIN] 免费游戏总奖金: #{total_free_win}")

    # 🎯 将免费游戏奖金加到用户余额上
    updated_state =
      if total_free_win > 0 do
        {:ok, numeric_id} = get_numeric_id(user_id)
        Logger.info("🎰 [FREE_SPINS_WIN] 为玩家#{user_id}(#{numeric_id})增加免费游戏奖金: #{total_free_win}")
        add_player_points(state, numeric_id, total_free_win)
      else
        state
      end

    # 🎯 添加详细的免费游戏数据调试日志
    Logger.info("🎰 [FREE_SPINS_DEBUG] ========== 免费游戏数据详细调试 ==========")
    Logger.info("🎰 [FREE_SPINS_DEBUG] 免费次数: #{free_times}")
    Logger.info("🎰 [FREE_SPINS_DEBUG] 数据键: #{inspect(Map.keys(free_games_data))}")

    # 检查每个免费游戏数据的完整性
    Enum.each(free_games_data, fn {key, game_data} ->
      Logger.info("🎰 [FREE_SPINS_DEBUG] 免费游戏#{key}数据:")
      Logger.info("🎰 [FREE_SPINS_DEBUG] - iconresult存在: #{Map.has_key?(game_data, "iconresult")}")
      Logger.info("🎰 [FREE_SPINS_DEBUG] - lineresult存在: #{Map.has_key?(game_data, "lineresult")}")

      Logger.info(
        "🎰 [FREE_SPINS_DEBUG] - lineresult类型: #{typeof(Map.get(game_data, "lineresult", []))}"
      )

      Logger.info(
        "🎰 [FREE_SPINS_DEBUG] - lineresult内容: #{inspect(Map.get(game_data, "lineresult", []))}"
      )

      Logger.info("🎰 [FREE_SPINS_DEBUG] - 完整数据: #{inspect(game_data, pretty: true)}")

      if Map.has_key?(game_data, "iconresult") do
        iconresult = Map.get(game_data, "iconresult")
        Logger.info("🎰 [FREE_SPINS_DEBUG] - iconresult键数量: #{map_size(iconresult)}")
        Logger.info("🎰 [FREE_SPINS_DEBUG] - iconresult键: #{inspect(Map.keys(iconresult))}")

        # 🎯 检查iconresult的数据完整性
        expected_keys = [
          "1",
          "2",
          "3",
          "4",
          "5",
          "6",
          "7",
          "8",
          "9",
          "10",
          "11",
          "12",
          "13",
          "14",
          "15"
        ]

        missing_keys = expected_keys -- Map.keys(iconresult)

        if length(missing_keys) > 0 do
          Logger.error("🎰 [FREE_SPINS_DEBUG] ❌ iconresult缺少键: #{inspect(missing_keys)}")
        end
      else
        Logger.error("🎰 [FREE_SPINS_DEBUG] ❌ 缺少iconresult字段")
      end

      # 🎯 检查lineresult的数据完整性
      lineresult = Map.get(game_data, "lineresult", [])

      if is_list(lineresult) do
        Logger.info("🎰 [FREE_SPINS_DEBUG] - lineresult是数组，长度: #{length(lineresult)}")

        if length(lineresult) > 0 do
          first_line = List.first(lineresult)
          Logger.info("🎰 [FREE_SPINS_DEBUG] - 第一条中奖线: #{inspect(first_line)}")

          # 检查中奖线数据格式
          if Map.has_key?(first_line, "line") and Map.has_key?(first_line, "num") do
            Logger.info("🎰 [FREE_SPINS_DEBUG] ✅ 中奖线格式正确")
          else
            Logger.error("🎰 [FREE_SPINS_DEBUG] ❌ 中奖线格式错误，缺少line或num字段")
          end
        end
      else
        Logger.error("🎰 [FREE_SPINS_DEBUG] ❌ lineresult不是数组类型")
      end
    end)

    # 发送免费游戏数据
    response = %{
      "mainId" => 5,
      # SC_SLOTNIU_FREEGAME_P
      "subId" => 1002,
      "data" => free_games_data
    }

    Logger.info("🎰 [FREE_SPINS_WIN] 发送免费游戏数据 - 用户: #{user_id}, 免费次数: #{free_times}")
    Logger.info("🎰 [FREE_SPINS_WIN] 完整响应数据: #{inspect(response, pretty: true)}")
    send_to_player_by_id(updated_state, user_id, response)

    # 🎯 发送积分更新通知（参考其他游戏的实现，使用SubID=8）
    if total_free_win > 0 do
      {:ok, numeric_id} = get_numeric_id(user_id)
      current_points = get_player_points(updated_state, numeric_id)

      response_money_update = %{
        # MainProto.Game
        "mainId" => 4,
        # Game.SC_ROOM_RESET_COIN_P - 这会触发前端updatePlayerMoney
        "subId" => 8,
        "data" => %{
          "playerid" => numeric_id,
          # 这个字段会更新前端myselfMoney
          "coin" => current_points
        }
      }

      Logger.info(
        "🎰 [FREE_SPINS_WIN] 发送积分更新通知 - 用户: #{user_id}, numeric_id: #{numeric_id}, 当前积分: #{current_points}, 增加: #{total_free_win}"
      )

      send_to_player_by_id(updated_state, user_id, response_money_update)
    end

    updated_state
  end

  # 处理EXPANDING WILD中奖
  defp handle_expanding_wild_win(state, user_id, turntable_result) do
    wild_type = turntable_result["wildtype"]
    Logger.info("🎰 [EXPANDING_WILD_WIN] 玩家#{user_id}中EXPANDING WILD奖励，类型: #{wild_type}")

    # 🎯 初始化玩家的EXPANDING WILD状态 - 参考旧项目实现
    player_wild_state = SlotNiuLogic.init_expanding_wild_state(wild_type)
    Logger.info("🎰 [EXPANDING_WILD_WIN] 初始化EXPANDING WILD状态: #{inspect(player_wild_state)}")

    # 🎯 重要修正：根据旧项目分析，EXPANDING WILD应该是渐进式的，不是5轮连续游戏
    # 玩家下次点击开始游戏时，应该进入带有EXPANDING WILD的普通游戏
    # 每次游戏消耗一次WILD次数，直到用完为止

    # 更新玩家的EXPANDING WILD状态
    updated_state = %{
      state
      | game_data: %{
          state.game_data
          | player_wild_states:
              Map.put(state.game_data.player_wild_states, user_id, player_wild_state)
        }
    }

    Logger.info("🎰 [EXPANDING_WILD_WIN] ✅ 玩家EXPANDING WILD状态已保存，下次游戏将应用WILD效果")

    # 🎯 发送转盘结果协议 - EXPANDING WILD中奖
    # EXPANDING WILD中奖时winmoney为0（因为是特殊游戏，不是直接金钱奖励）
    turntable_response = %{
      "mainId" => 5,
      # SC_SLOTNIU_SPINSTART_P
      "subId" => 1004,
      "data" => %{
        "luckyspinid" => turntable_result["luckyspinid"],
        # EXPANDING WILD中奖时为0
        "winmoney" => 0,
        # Wild类型
        "wildtype" => wild_type,
        # 特殊游戏不直接给钱
        "changemoney" => 0
      }
    }

    send_to_player_by_id(updated_state, user_id, turntable_response)

    Logger.info("🎰 [EXPANDING_WILD_WIN] ✅ 转盘结果已发送，玩家下次游戏将应用EXPANDING WILD效果")
    Logger.info("🎰 [EXPANDING_WILD_WIN] 🎯 重要：根据旧项目分析，EXPANDING WILD是渐进式的")
    Logger.info("🎰 [EXPANDING_WILD_WIN] 🎯 玩家每次点击开始游戏时，会应用WILD效果并消耗一次使用次数")
    Logger.info("🎰 [EXPANDING_WILD_WIN] 🎯 直到WILD次数用完为止，这样更符合旧项目的实现")

    updated_state
  end

  # 生成免费游戏数据 - 修复：使用玩家当前选中的倍率并正确处理PRIZE奖池
  defp generate_free_games_data(state, free_times, player_odds \\ 1, user_id \\ nil) do
    Logger.info("🎰 [FREE_GAMES_DATA] 开始生成#{free_times}次免费游戏数据，使用玩家倍率: #{player_odds}")

    # 获取游戏配置
    config = state.game_data.config
    difen = config.difen
    # 🎯 修复：获取玩家当前积分，而不是使用bet_rate_num
    current_money =
      if user_id do
        {:ok, numeric_id} = get_numeric_id(user_id)
        get_player_points(state, numeric_id)
      else
        # 默认积分（如果无法获取玩家ID）
        100_000
      end

    # 🎯 修复：累积处理PRIZE奖池变化
    # 初始PRIZE奖池金额
    initial_prize_pool = state.game_data.prize_pool

    # 为每次免费游戏生成结果，并累积更新PRIZE奖池
    {free_results, final_prize_pool} =
      Enum.reduce(1..free_times, {%{}, initial_prize_pool}, fn i,
                                                               {acc_results, current_prize_pool} ->
        # 🎯 修复：免费游戏使用玩家当前选中的倍率，并传递当前的PRIZE奖池状态
        # 使用完整配置以确保游戏逻辑能访问所有必要的配置项
        game_config =
          Map.merge(state.game_data.full_config, %{
            jackpot_amount: state.game_data.jackpot_amount,
            prize_pool: current_prize_pool
          })

        # 🎯 获取玩家的EXPANDING WILD状态（免费游戏中也可能有EXPANDING WILD）
        player_wild_state =
          if user_id do
            Map.get(state.game_data.player_wild_states, user_id)
          else
            nil
          end

        # 🎯 修复：免费游戏使用专用函数，限制牛头和鹿头数量不超过2个
        game_result =
          SlotNiuLogic.generate_free_game_result(
            player_odds,
            current_money,
            difen,
            game_config,
            player_wild_state
          )

        # 🎯 从游戏结果中获取更新后的PRIZE奖池金额
        updated_prize_pool = Map.get(game_result, "prize", current_prize_pool)
        prize_used = Map.get(game_result, "prize_used", 0)
        prize_increase = Map.get(game_result, "prize_increase", 0)

        Logger.info(
          "🎰 [FREE_GAMES_DATA] 生成第#{i}次免费游戏: 倍率#{player_odds}, 积分#{current_money}, 牛数#{game_result["niunum"]}, 中奖#{game_result["winmoney"]}"
        )

        Logger.info(
          "🎰 [FREE_GAMES_DATA] 第#{i}次PRIZE奖池变化: 当前#{current_prize_pool} -> 更新后#{updated_prize_pool} (使用#{prize_used}, 增加#{prize_increase})"
        )

        # 累积结果
        updated_results = Map.put(acc_results, Integer.to_string(i), game_result)
        {updated_results, updated_prize_pool}
      end)

    Logger.info("🎰 [FREE_GAMES_DATA] ✅ 免费游戏数据生成完成，总数: #{map_size(free_results)}")
    Logger.info("🎰 [FREE_GAMES_DATA] 🎯 PRIZE奖池变化: #{initial_prize_pool} -> #{final_prize_pool}")

    # 返回免费游戏结果和最终的PRIZE奖池金额
    {free_results, final_prize_pool}
  end

  # 处理Jackpot记录请求（使用全局管理器）
  # 参考前端FMWJackpotRecordLayer.ts的onUpdateRecordInfo方法期望的数据格式
  defp handle_jackpot_list_request(state, user_id, data) do
    Logger.info("🎰 [JACKPOT_LIST] ========== 处理Jackpot记录请求 ==========")
    Logger.info("🎰 [JACKPOT_LIST] 用户: #{user_id}")
    Logger.info("🎰 [JACKPOT_LIST] 请求数据: #{inspect(data, pretty: true)}")
    Logger.info("🎰 [JACKPOT_LIST] 协议: CS_SLOTNIU_JPLIST_P (1005) -> SC_SLOTNIU_JPLIST_P (1006)")

    case get_slotniu_jackpot_records(state) do
      {:ok, records} ->
        Logger.info("🎰 [JACKPOT_LIST] ✅ 成功获取全局Jackpot记录")
        Logger.info("🎰 [JACKPOT_LIST] 📊 原始记录数量: #{length(records)}")

        # 验证记录数据格式（使用统一的流水表格式）
        validated_records =
          Enum.filter(records, fn record ->
            has_required_fields =
              Map.has_key?(record, "player_id") and
                Map.has_key?(record, "player_name") and
                Map.has_key?(record, "win_amount")

            if not has_required_fields do
              Logger.warning("🎰 [JACKPOT_LIST] ⚠️ 发现无效记录: #{inspect(record)}")
            end

            has_required_fields
          end)
          |> Enum.map(fn record ->
            # 从game_data中提取SlotNiu特有字段
            game_data = record["game_data"] || %{}

            # 格式化时间为前端期望的格式 (MM-dd HH:mm)
            formatted_time = format_timestamp_for_slotniu_frontend(record["win_time"])

            # 转换为SlotNiu前端期望的格式（按照slotniu_jackpot.ex的formatted_record格式）
            %{
              "playerid" => record["player_id"],
              "name" => record["player_name"],
              "headid" => record["avatar_id"] || 1,
              "wxheadurl" => record["avatar_url"] || "",
              "niunum" => Map.get(game_data, "niunum", Map.get(game_data, "bull_count", 0)),
              "bet" => Map.get(game_data, "bet", record["bet_amount"] || 0),
              "winscore" => record["win_amount"],
              "time" => formatted_time,
              "mult" => Map.get(game_data, "mult", Map.get(game_data, "multiplier", 1))
            }
          end)

        Logger.info("🎰 [JACKPOT_LIST] 📊 有效记录数量: #{length(validated_records)}")

        # 前端期望的是以索引为键的对象格式，参考TpMasterClient的onUpdateRecordInfo处理
        # 将数组转换为以索引为键的对象格式
        records_map =
          validated_records
          |> Enum.with_index()
          |> Enum.into(%{}, fn {record, index} -> {to_string(index), record} end)

        # 构建响应数据，确保与前端期望的格式完全匹配
        response = %{
          "mainId" => 5,
          # SC_SLOTNIU_JPLIST_P
          "subId" => 1006,
          # 直接发送记录映射，不嵌套在"winners"中
          "data" => records_map
        }

        Logger.info("🎰 [JACKPOT_LIST] ✅ 构建响应数据完成")
        Logger.info("🎰 [JACKPOT_LIST] 📊 响应记录数量: #{map_size(records_map)}")

        # 显示前3条记录的详细信息用于调试
        if length(validated_records) > 0 do
          Logger.info("🎰 [JACKPOT_LIST] 📋 记录详情（前3条）:")

          validated_records
          |> Enum.take(3)
          |> Enum.with_index()
          |> Enum.each(fn {record, index} ->
            Logger.info(
              "🎰 [JACKPOT_LIST] 📋 [#{index}] 玩家: #{record["name"]}, 牛数: #{record["niunum"]}, 奖金: #{record["winscore"]}, 时间: #{record["time"]}"
            )
          end)
        end

        # 显示记录统计信息
        Logger.info("🎰 [JACKPOT_LIST] 📈 流水表记录统计: #{length(validated_records)}条有效记录")

        send_to_player_by_id(state, user_id, response)
        Logger.info("🎰 [JACKPOT_LIST] ✅ Jackpot记录响应已发送")

      {:error, reason} ->
        Logger.error("🎰 [JACKPOT_LIST] ❌ 获取全局记录失败: #{reason}")
        # 发送空记录作为降级处理
        response = %{
          "mainId" => 5,
          # SC_SLOTNIU_JPLIST_P
          "subId" => 1006,
          # 空记录
          "data" => %{}
        }

        send_to_player_by_id(state, user_id, response)
        Logger.info("🎰 [JACKPOT_LIST] ⚠️ 已发送空记录作为降级处理")
    end

    state
  end

  # 🎯 发送游戏配置给玩家 (前端通过onGameConfig接收)
  defp send_game_config(state, user_id) do
    Logger.info("📤 [SLOTNIU_CONFIG] ========== 发送游戏配置 ==========")
    Logger.info("📤 [SLOTNIU_CONFIG] 用户: #{user_id}")
    Logger.info("📤 [SLOTNIU_CONFIG] 房间ID: #{state.id}")

    # 🔧 添加防御性检查 - 确保game_data和config存在
    if is_nil(state.game_data) or is_nil(state.game_data.config) do
      Logger.error("❌ [SLOTNIU_ERROR] state.game_data或config为nil，无法发送游戏配置")
    else
      # 获取玩家信息
      {:ok, numeric_id} = get_numeric_id(user_id)

      # 获取玩家当前积分（参考longhu方式，使用numeric_id）
      current_money = get_player_points(state, numeric_id)
      Logger.info("📤 [SLOTNIU_CONFIG] 玩家积分: #{current_money}")

      player_data = Map.get(state.players, numeric_id)

      player_name =
        if player_data do
          Map.get(player_data.user, :username, "玩家#{numeric_id}")
        else
          "玩家#{numeric_id}"
        end

      headid = Map.get(player_data.user, :avatar_id, 1)
      Logger.info("📤 [SLOTNIU_CONFIG] 玩家信息 - ID: #{numeric_id}, 名称: #{player_name}")

      # 🎯 获取玩家上次使用的倍率（参考TpMasterClient客户端lastodds处理）
      player_last_odds = get_player_last_odds(state, numeric_id)
      Logger.info("📤 [SLOTNIU_CONFIG] 玩家上次倍率: #{player_last_odds}")

      # 🔧 修复积分显示问题 - 前端期望原始积分值
      # 前端使用moneyFormat处理积分显示：money / Config.SCORE_RATE
      # 所以服务端应该发送原始积分值，前端自己除以SCORE_RATE显示
      # 例如：用户有499.4积分，服务端发送49940，前端显示499.40

      Logger.info("📤 [SLOTNIU_CONFIG] 积分处理:")
      Logger.info("📤 [SLOTNIU_CONFIG] - 玩家原始积分: #{current_money}")
      Logger.info("📤 [SLOTNIU_CONFIG] - 发送给前端: #{current_money}（前端会除以SCORE_RATE显示）")

      # 🔧 关键：构建游戏配置消息
      # 客户端的onGameConfig通过onToOtherRoom调用，绑定的是MainProto.XC (5) + XC.XC_ROOM_INFO_P (0)
      # 参考TpMasterClient客户端代码，onGameConfig期望的完整数据格式
      message = %{
        # XC协议 - 子游戏服务器和客户端交互
        "mainId" => 5,
        # XC_ROOM_INFO_P - 房间数据协议（会触发onToOtherRoom -> onGameConfig）
        "subId" => 0,
        "data" => %{
          # 🎯 游戏配置核心字段（参考客户端onGameConfig处理）
          # 底分 - 前端会除以SCORE_RATE显示
          "difen" => state.game_data.config.difen,
          # 下注倍率配置数组 - 客户端用于生成倍率选项
          "odds" => state.game_data.config.odds_config,
          # Jackpot奖池 (allJackpotNum) - 前端会除以SCORE_RATE显示
          "jackpot" => state.game_data.jackpot_amount,
          # 🎯 修复：前端期望的jackpotnum字段
          "jackpotnum" => state.game_data.jackpot_amount,
          # 玩家个人Prize奖池 (font_prize) - 前端会除以SCORE_RATE显示
          "prize" => state.game_data.prize_pool,

          # 🎯 玩家倍率恢复（参考客户端lastodds处理）
          # 玩家上次使用的倍率 - 客户端用于恢复倍率选择
          "lastodds" => player_last_odds,

          # 下注限制配置
          # 最小下注金额
          "BetNeed" => state.game_data.config.min_bet,
          # 最大下注金额
          "BetMax" => state.game_data.config.max_bet,

          # 房间基础信息
          # 房间ID
          "room_id" => to_string(state.id),
          # 当前回合数
          "roundid" => state.game_data.current_round,
          # 房间状态：1-游戏中
          "roomstate" => 1,

          # 游戏状态信息（参考客户端期望字段）
          # 游戏状态：0-等待开始
          "gamestate" => 0,
          # 当前免费次数
          "freetimes" => 0,
          # 总免费次数
          "totalfreetimes" => 0,

          # 玩家信息列表
          "playerlist" => %{
            "1" => %{
              # 座位号
              "seat" => 1,
              # 玩家数字ID
              "playerid" => numeric_id,
              # 玩家名称
              "name" => player_name,
              # 玩家原始积分（前端会除以SCORE_RATE显示）
              "money" => current_money,
              # 头像ID
              "headid" => headid,
              # 自定义头像URL
              "headurl" => "",
              # 玩家状态：1-正常
              "status" => 1,
              # 玩家当前倍率
              "currentodds" => player_last_odds
            }
          }
        }
      }

      Logger.info("📤 [SLOTNIU_CONFIG] 游戏配置详情:")

      Logger.info(
        "📤 [SLOTNIU_CONFIG] - 协议: MainID=#{message["mainId"]}, SubID=#{message["subId"]}"
      )

      Logger.info("📤 [SLOTNIU_CONFIG] - 底分: #{state.game_data.config.difen}")
      Logger.info("📤 [SLOTNIU_CONFIG] - 倍率: #{inspect(state.game_data.config.odds_config)}")

      Logger.info(
        "📤 [SLOTNIU_CONFIG] - Jackpot (allJackpotNum): #{state.game_data.jackpot_amount}"
      )

      Logger.info("📤 [SLOTNIU_CONFIG] - Prize (font_prize): #{state.game_data.prize_pool}")
      Logger.info("📤 [SLOTNIU_CONFIG] - 玩家金币: #{current_money}")
      Logger.info("📤 [SLOTNIU_CONFIG] - 玩家上次倍率: #{player_last_odds}")

      # 使用RoomBase的方式发送游戏配置 (前端通过onToOtherRoom -> onGameConfig接收)
      case Map.get(state.players, numeric_id) do
        nil ->
          Logger.warning("📤 [SLOTNIU_CONFIG] ⚠️ 玩家不在房间中: #{user_id} (numeric_id: #{numeric_id})")

        player_data ->
          player = %{user_id: user_id, user_info: player_data.user}
          send_to_player(state, player, message)
          Logger.info("📤 [SLOTNIU_CONFIG] ✅ 游戏配置发送完成")
      end
    end
  end

  # 发送玩家列表信息给指定玩家
  defp send_player_list_to_user(state, user_id) do
    Logger.info("📤 [SLOTNIU_PLAYER_LIST] ========== 发送玩家列表信息 ==========")
    Logger.info("📤 [SLOTNIU_PLAYER_LIST] 用户: #{user_id}")
    Logger.info("📤 [SLOTNIU_PLAYER_LIST] 房间ID: #{state.id}")

    # 获取玩家信息
    {:ok, numeric_id} = get_numeric_id(user_id)

    # 获取玩家当前积分（参考longhu方式，使用numeric_id）
    current_money = get_player_points(state, numeric_id)
    player_name = "玩家#{numeric_id}"

    Logger.info(
      "📤 [SLOTNIU_PLAYER_LIST] 玩家信息 - ID: #{numeric_id}, 名称: #{player_name}, 积分: #{current_money}"
    )

    # 构建玩家列表消息
    player_list_message = %{
      # 主协议ID为4
      "mainId" => 4,
      # 子协议ID为2 - 玩家列表信息
      "subId" => 2,
      "data" => %{
        "playerlist" => %{
          "1" => %{
            "seat" => 1,
            # 玩家ID
            "playerid" => numeric_id,
            # 玩家名称
            "name" => player_name,
            # 玩家原始积分
            "money" => current_money,
            # 头像ID
            "headid" => 1,
            # 头像URL
            "headurl" => "",
            # 玩家状态：1-正常
            "status" => 1
          }
        },
        "room_id" => to_string(state.id),
        # 房间玩家数量
        "player_count" => map_size(state.players)
      }
    }

    Logger.info("📤 [SLOTNIU_PLAYER_LIST] 玩家列表详情:")

    Logger.info(
      "📤 [SLOTNIU_PLAYER_LIST] - 协议: MainID=#{player_list_message["mainId"]}, SubID=#{player_list_message["subId"]}"
    )

    Logger.info("📤 [SLOTNIU_PLAYER_LIST] - 房间ID: #{state.id}")
    Logger.info("📤 [SLOTNIU_PLAYER_LIST] - 玩家数量: #{map_size(state.players)}")

    Logger.info(
      "📤 [SLOTNIU_PLAYER_LIST] - 玩家信息: #{inspect(player_list_message["data"]["playerlist"])}"
    )

    # 发送消息给指定玩家
    send_to_player_by_id(state, user_id, player_list_message)

    Logger.info("📤 [SLOTNIU_PLAYER_LIST] ✅ 玩家列表信息发送完成")
  end

  # 发送房间信息给指定用户 (前端通过onRoomInfo接收)
  defp send_room_info_to_user(state, user_id) do
    Logger.info("📤 [SLOTNIU_ROOM_INFO] ========== 发送房间信息 ==========")
    Logger.info("📤 [SLOTNIU_ROOM_INFO] 用户: #{user_id}")
    Logger.info("📤 [SLOTNIU_ROOM_INFO] 房间ID: #{state.id}")

    # 获取玩家信息
    {:ok, numeric_id} = get_numeric_id(user_id)

    # 获取玩家当前积分（参考longhu方式，使用numeric_id）
    current_money = get_player_points(state, numeric_id)

    Logger.info("📤 [SLOTNIU_ROOM_INFO] 玩家积分: #{current_money}")

    player_data = Map.get(state.players, numeric_id)

    player_name =
      if player_data do
        Map.get(player_data.user, :nickname, "玩家#{numeric_id}")
      else
        "玩家#{numeric_id}"
      end

    Logger.info("📤 [SLOTNIU_ROOM_INFO] 玩家信息 - ID: #{numeric_id}, 名称: #{player_name}")

    # 🔧 关键修复：构建房间信息消息 (前端通过onRoomInfo接收，包含游戏配置)
    # 根据IndiaGameClient/Protocol.ts，客户端onRoomInfo绑定的是：
    # MainProto.Game (4) + Game.SC_ROOM_INFO_P (2)
    # 因此服务端必须发送MainID=4, SubID=2的消息
    room_info_message = %{
      # Game协议 - 游戏逻辑协议
      "mainId" => 4,
      # SC_ROOM_INFO_P - 房间数据协议
      "subId" => 2,
      "data" => %{
        "room_id" => to_string(state.id),
        "roundid" => state.game_data.current_round,
        # 房间状态：1-游戏中
        "roomstate" => 1,
        # 底分
        "difen" => state.game_data.config.difen,
        "odds" => state.game_data.config.odds_config,
        # Jackpot奖池 (allJackpotNum)
        "jackpot" => state.game_data.jackpot_amount,
        # 🎯 修复：前端期望的jackpotnum字段
        "jackpotnum" => state.game_data.jackpot_amount,
        # 玩家个人Prize奖池 (font_prize)
        "prize" => state.game_data.prize_pool,
        "BetNeed" => state.game_data.config.min_bet,
        "BetMax" => state.game_data.config.max_bet,
        # 单聊费用
        "singlechatfee" => 0,
        # 打赏荷官费用
        "tipdealerfee" => 0,
        "playerlist" => %{
          "1" => %{
            "seat" => 1,
            "playerid" => numeric_id,
            "name" => player_name,
            # 玩家原始积分
            "money" => current_money,
            "headid" => 1,
            "headurl" => "",
            # 玩家状态：1-正常
            "status" => 1
          }
        }
      }
    }

    Logger.info("📤 [SLOTNIU_ROOM_INFO] 房间信息详情:")

    Logger.info(
      "📤 [SLOTNIU_ROOM_INFO] - 协议: MainID=#{room_info_message["mainId"]}, SubID=#{room_info_message["subId"]}"
    )

    Logger.info("📤 [SLOTNIU_ROOM_INFO] - 底分: #{state.game_data.config.difen}")
    Logger.info("📤 [SLOTNIU_ROOM_INFO] - 倍率: #{inspect(state.game_data.config.odds_config)}")
    Logger.info("📤 [SLOTNIU_ROOM_INFO] - Jackpot: #{state.game_data.jackpot_amount}")

    Logger.info(
      "📤 [SLOTNIU_ROOM_INFO] - 玩家列表: #{inspect(room_info_message["data"]["playerlist"])}"
    )

    # 使用RoomBase的方式发送房间信息 (前端通过onRoomInfo接收)
    case Map.get(state.players, numeric_id) do
      nil ->
        Logger.warning("📤 [SLOTNIU_ROOM_INFO] ⚠️ 玩家不在房间中: #{user_id} (numeric_id: #{numeric_id})")

      player_data ->
        player = %{user_id: user_id, user_info: player_data.user}
        send_to_player(state, player, room_info_message)
        Logger.info("📤 [SLOTNIU_ROOM_INFO] ✅ 房间信息发送完成")
    end
  end

  # 🎯 更新玩家的倍率索引记录
  defp update_player_odds_index(state, numeric_id, odds_index) do
    case Map.get(state.players, numeric_id) do
      nil ->
        Logger.warning("🔧 [UPDATE_ODDS] 玩家#{numeric_id}不存在，无法更新倍率索引")
        state

      player_data ->
        # 更新玩家的倍率索引
        updated_user = Map.put(player_data.user, :last_odds_index, odds_index)
        updated_player_data = %{player_data | user: updated_user}
        updated_players = Map.put(state.players, numeric_id, updated_player_data)

        Logger.info("🔧 [UPDATE_ODDS] 玩家#{numeric_id}倍率索引更新为: #{odds_index}")
        %{state | players: updated_players}
    end
  end

  # 🎯 问题一修复：获取玩家上次使用的倍率（修复前端减1逻辑）
  # 客户端逻辑分析：
  # 1. 客户端接收lastodds值
  # 2. 在bets数组中查找：if(bets[`${i}`] == info["lastodds"]) { this.meAddBeiLv = Number(i) - 1; }
  # 3. 计算betMoney：this.betMoney = this.bets[this.meAddBeiLv] * Config.SCORE_RATE
  #
  # 🎯 关键发现：前端会对找到的索引减1！
  # 例如：如果玩家实际想要索引2，前端期望在索引3位置找到匹配值
  # 所以服务端需要返回 索引+1 位置的倍率值
  defp get_player_last_odds(state, numeric_id) do
    # 🔧 添加防御性检查 - 确保game_data和config存在
    if is_nil(state.game_data) or is_nil(state.game_data.config) do
      Logger.error("❌ [SLOTNIU_ERROR] state.game_data或config为nil，无法获取玩家倍率")
      # 返回默认倍率
      1
    else
      odds_config = state.game_data.config.odds_config

      case Map.get(state.players, numeric_id) do
        nil ->
          # 新玩家，默认想要索引0，所以返回索引1的倍率
          case odds_config do
            [_first, second | _] ->
              Logger.info("🔧 [LASTODDS] 新玩家，返回索引1倍率: #{second} → 前端索引0")
              second

            [single] ->
              # 只有一个倍率，返回它自己
              Logger.info("🔧 [LASTODDS] 只有一个倍率: #{single}")
              single

            [] ->
              Logger.warning("🔧 [LASTODDS] 倍率配置为空")
              1
          end

        player_data ->
          # 老玩家，获取上次的倍率索引
          last_odds_index = Map.get(player_data.user, :last_odds_index, 0)
          Logger.info("🔧 [LASTODDS] 玩家#{numeric_id}上次倍率索引: #{last_odds_index}")

          case odds_config do
            odds_list when is_list(odds_list) and length(odds_list) > 0 ->
              # 确保索引有效
              safe_index = max(0, min(last_odds_index, length(odds_list) - 1))

              # 🎯 关键修复：返回 safe_index + 1 位置的倍率
              target_index = safe_index + 1

              if target_index < length(odds_list) do
                result_odds = Enum.at(odds_list, target_index)

                Logger.info(
                  "🔧 [LASTODDS] 返回索引#{target_index}倍率: #{result_odds} → 前端索引#{safe_index}"
                )

                result_odds
              else
                # 如果是最后一个索引，返回第二个倍率（让前端回到索引0）
                if length(odds_list) > 1 do
                  result_odds = Enum.at(odds_list, 1)
                  Logger.info("🔧 [LASTODDS] 最后索引特殊处理，返回索引1倍率: #{result_odds} → 前端索引0")
                  result_odds
                else
                  # 只有一个倍率
                  result_odds = Enum.at(odds_list, 0)
                  Logger.info("🔧 [LASTODDS] 单倍率处理: #{result_odds}")
                  result_odds
                end
              end

            [] ->
              Logger.warning("🔧 [LASTODDS] 倍率配置为空")
              1
          end
      end
    end
  end

  # 注释：已删除临时积分配置函数，现在使用longhu的积分管理方式
  # send_game_config_with_temp_money 已被 send_game_config 替代

  # 广播玩家数量变化通知
  defp broadcast_player_count_change(state) do
    message = %{
      "mainId" => 5,
      "subId" => @protocol_ids.sc_playerlist,
      "data" => %{
        "totalplayernum" => map_size(state.players)
      }
    }

    Logger.info("📤 [BROADCAST_PLAYER_COUNT] 广播玩家数量变化 - 房间总人数: #{map_size(state.players)}")
    broadcast_to_room(state, message)
  end

  # 发送消息给指定用户 - 使用继承自RoomBase的send_to_player
  defp send_to_player_by_id(state, user_id, message) do
    Logger.info("📤 [SLOTNIU_SEND] ========== 发送消息给用户 ==========")
    Logger.info("📤 [SLOTNIU_SEND] 目标用户: #{user_id}")
    Logger.info("📤 [SLOTNIU_SEND] 消息完整内容: #{inspect(message, pretty: true, limit: :infinity)}")
    Logger.info("📤 [SLOTNIU_SEND] 消息类型: MainID=#{message["mainId"]}, SubID=#{message["subId"]}")
    Logger.info("📤 [SLOTNIU_SEND] 发送时间: #{DateTime.utc_now()}")
    Logger.info("📤 [SLOTNIU_SEND] 🔍 房间玩家状态检查:")
    Logger.info("📤 [SLOTNIU_SEND]    - 房间总玩家数: #{map_size(state.players)}")
    Logger.info("📤 [SLOTNIU_SEND]    - 房间玩家列表: #{inspect(Map.keys(state.players))}")

    # 获取玩家的numeric_id
    {:ok, numeric_id} = get_numeric_id(user_id)

    # 获取玩家对象（使用numeric_id作为键）
    case Map.get(state.players, numeric_id) do
      nil ->
        Logger.warning("📤 [SLOTNIU_SEND] ⚠️ 玩家不在房间中: #{user_id} (numeric_id: #{numeric_id})")
        Logger.warning("📤 [SLOTNIU_SEND] 🔍 调试信息:")
        Logger.warning("📤 [SLOTNIU_SEND]    - 查找的用户ID: #{inspect(user_id)}")
        Logger.warning("📤 [SLOTNIU_SEND]    - 查找的数字ID: #{inspect(numeric_id)}")
        Logger.warning("📤 [SLOTNIU_SEND]    - 用户ID类型: #{inspect(typeof(user_id))}")
        Logger.warning("📤 [SLOTNIU_SEND]    - 房间中的键: #{inspect(Map.keys(state.players))}")
        Logger.info("📤 [SLOTNIU_SEND] 🔄 使用直接广播方式发送")
        # 直接发送消息
        CypridinaWeb.Endpoint.broadcast!("user:#{user_id}", "private_message", message)
        Logger.info("📤 [SLOTNIU_SEND] ✅ 直接广播完成")

      player_data ->
        # 构建player对象用于RoomBase的send_to_player（使用.user字段）
        player = %{user_id: user_id, user_info: player_data.user}
        Logger.info("📤 [SLOTNIU_SEND] ✅ 玩家在房间中，使用RoomBase发送")

        Logger.info(
          "📤 [SLOTNIU_SEND] 🎯 玩家信息: #{inspect(Map.get(player_data.user, :nickname, "未知"))}"
        )

        Logger.info("📤 [SLOTNIU_SEND] 🎯 玩家当前积分: #{inspect(get_player_points(state, numeric_id))}")
        send_to_player(state, player, message)
        Logger.info("📤 [SLOTNIU_SEND] ✅ RoomBase发送完成")
    end
  end

  # 获取变量类型的辅助函数
  defp typeof(value) do
    cond do
      is_atom(value) -> :atom
      is_binary(value) -> :binary
      is_integer(value) -> :integer
      is_float(value) -> :float
      is_list(value) -> :list
      is_map(value) -> :map
      is_tuple(value) -> :tuple
      is_pid(value) -> :pid
      is_reference(value) -> :reference
      is_function(value) -> :function
      true -> :unknown
    end
  end

  # 注释：已删除临时积分同步函数，现在使用longhu的积分管理方式
  # 积分变化通过PlayerData.add_points和subtract_points实时同步到数据库

  # 更新玩家信息（使用numeric_id作为键）
  defp update_player_info(state, user_id, updated_user_info) do
    {:ok, numeric_id} = get_numeric_id(user_id)

    case Map.get(state.players, numeric_id) do
      nil ->
        state

      player ->
        updated_player = %{player | user: updated_user_info}
        updated_players = Map.put(state.players, numeric_id, updated_player)
        %{state | players: updated_players}
    end
  end

  # 根据user_id获取numeric_id
  defp get_numeric_id(user_id) do
    try do
      if is_integer(user_id) do
        {:ok, user_id}
      else
        case Ash.get(Cypridina.Accounts.User, user_id) do
          {:ok, user} ->
            {:ok, user.numeric_id}

          {:error, reason} ->
            Logger.warning(
              "⚠️ [GET_NUMERIC_ID] 获取用户numeric_id失败 - user_id: #{user_id}, 原因: #{inspect(reason)}"
            )

            virtual_numeric_id = generate_virtual_numeric_id(user_id)
            {:ok, virtual_numeric_id}
        end
      end
    rescue
      error ->
        Logger.error(
          "❌ [GET_NUMERIC_ID] 获取numeric_id异常 - user_id: #{user_id}, 错误: #{inspect(error)}"
        )

        virtual_numeric_id = generate_virtual_numeric_id(user_id)
        {:ok, virtual_numeric_id}
    end
  end

  # 生成虚拟numeric_id
  defp generate_virtual_numeric_id(user_id) do
    cond do
      is_binary(user_id) and String.length(user_id) == 36 ->
        user_id
        |> String.replace("-", "")
        |> String.slice(0, 8)
        |> String.to_integer(16)
        |> rem(100_000_000)
        |> Kernel.+(10_000_000)

      is_binary(user_id) ->
        hash = :erlang.phash2(user_id, 100_000_000)
        hash + 10_000_000

      is_integer(user_id) ->
        user_id

      true ->
        :rand.uniform(90_000_000) + 10_000_000
    end
  end

  # ==================== GenServer 回调函数 ====================
  # 注意：这些函数现在主要用于兼容性，实际的玩家管理由RoomBase处理

  # 处理其他调用 - 委托给RoomBase
  @impl true
  def handle_call(request, from, state) do
    Logger.info("🎰 [SLOTNIU_CALL] 委托调用给RoomBase: #{inspect(request)}")
    super(request, from, state)
  end

  # 处理异步消息 - 委托给RoomBase
  @impl true
  def handle_cast(request, state) do
    Logger.info("🎰 [SLOTNIU_CAST] 委托异步消息给RoomBase: #{inspect(request)}")
    super(request, state)
  end

  # 处理其他信息 - 委托给RoomBase
  @impl true
  def handle_info(info, state) do
    case info do
      :update_prize_pools ->
        handle_prize_pools_update(state)

      _ ->
        Logger.info("🎰 [SLOTNIU_INFO] 委托信息给RoomBase: #{inspect(info)}")
        super(info, state)
    end
  end

  # 处理奖池更新
  defp handle_prize_pools_update(state) do
    Logger.info("🎰 [PRIZE_UPDATE] ========== 更新奖池金额 ==========")

    # 🔧 添加防御性检查 - 确保game_data存在
    if is_nil(state.game_data) do
      Logger.error("❌ [PRIZE_UPDATE] state.game_data为nil，跳过奖池更新")
      schedule_prize_update()
      {:noreply, state}
    else
      # 从真实奖池系统获取最新的Jackpot金额
      current_jackpot = state.game_data.jackpot_amount
      real_jackpot = get_jackpot_amount_safe(state)

      Logger.info("🎰 [JACKPOT_UPDATE] 从奖池系统获取实时金额")
      Logger.info("🎰 [JACKPOT_UPDATE] 当前缓存: #{current_jackpot}, 实时奖池: #{real_jackpot}")
      Logger.info("🎰 [JACKPOT_UPDATE] Prize: 由玩家游戏累积，不定时更新")

      # 更新game_data（使用真实奖池金额）
      new_game_data = %{
        state.game_data
        | jackpot_amount: real_jackpot,
          last_jackpot_amount: current_jackpot
      }

      new_state = %{state | game_data: new_game_data}

      # 发送Jackpot更新消息给所有玩家
      send_jackpot_update_to_all_players(new_state, real_jackpot)

      # 安排下次更新
      schedule_prize_update()

      {:noreply, new_state}
    end
  end

  # 发送Jackpot更新消息给所有玩家
  defp send_jackpot_update_to_all_players(state, jackpot_amount) do
    Logger.info("🎰 [JACKPOT_BROADCAST] 广播Jackpot更新给所有玩家")
    Logger.info("🎰 [JACKPOT_BROADCAST] - Jackpot (allJackpotNum): #{jackpot_amount}")

    # 构建Jackpot更新消息
    # 参考TpMasterClient前端代码onUpdateJackpot函数
    # 使用正确的协议：SC_SLOTNIU_JACKPOT_P (1007)
    jackpot_message = %{
      # XC协议
      "mainId" => 5,
      # SC_SLOTNIU_JACKPOT_P - Jackpot同步协议
      "subId" => 1007,
      "data" => %{
        # 前端onUpdateJackpot期望的字段
        "jackpot" => jackpot_amount
      }
    }

    # 广播给房间内所有玩家
    broadcast_to_room(state, jackpot_message)
  end

  # 发送PRIZE奖池更新消息给指定玩家 协议是错的
  defp send_prize_update_to_player(state, user_id, prize_amount) do
    Logger.info("🎰 [PRIZE_UPDATE] 发送PRIZE奖池更新给玩家")
    Logger.info("🎰 [PRIZE_UPDATE] - 用户: #{user_id}")
    Logger.info("🎰 [PRIZE_UPDATE] - Prize奖池: #{prize_amount}")

    # 获取玩家信息
    {:ok, numeric_id} = get_numeric_id(user_id)
    current_money = get_player_points(state, numeric_id)

    # 构建完整的房间信息更新消息
    # 参考客户端onGameConfig中的prize字段处理
    # 客户端通过onRoomInfo -> onGameConfig来更新PRIZE奖池
    prize_message = %{
      # XC协议
      "mainId" => 5,
      # XC_ROOM_INFO_P - 房间数据协议，会触发onGameConfig
      "subId" => 0,
      "data" => %{
        # 🎯 关键字段：客户端onGameConfig期望的完整数据
        # PRIZE奖池 - 客户端会除以SCORE_RATE显示
        "prize" => prize_amount,
        # Jackpot奖池 - 保持同步
        "jackpot" => state.game_data.jackpot_amount,
        # 底分
        "difen" => state.game_data.config.difen,
        # 倍率配置
        "odds" => state.game_data.config.odds_config,

        # 玩家信息
        "playerid" => numeric_id,
        "current_money" => current_money,

        # 房间状态
        "room_id" => to_string(state.id),
        "roundid" => state.game_data.current_round,
        "roomstate" => 1,
        "gamestate" => 0
      }
    }

    Logger.info("🎰 [PRIZE_UPDATE] 发送完整房间信息更新 - 用户: #{user_id}, 奖池: #{prize_amount}")
    send_to_player_by_id(state, user_id, prize_message)

    Logger.info("🎰 [PRIZE_UPDATE] ✅ PRIZE更新发送完成")
  end

  @doc """
  发送PRIZE奖池状态更新给玩家

  **用途**：
  - 倍率切换时通知玩家PRIZE奖池状态
  - 奖池金额变化时的实时更新
  """
  defp send_prize_status_to_player(state, user_id, prize_status) do
    {:ok, numeric_id} = get_numeric_id(user_id)

    message = %{
      # XC协议
      "mainId" => 5,
      # 自定义PRIZE状态更新协议
      "subId" => 1020,
      "data" => %{
        "prize_pool" => prize_status.current_amount,
        "multiplier" => prize_status.current_multiplier,
        "collection_rate" => prize_status.collection_rate,
        "display_amount" => prize_status.display_amount,
        "is_empty" => prize_status.is_empty
      }
    }

    send_to_player_by_id(state, user_id, message)
    Logger.info("🎰 [PRIZE_STATUS] 发送PRIZE奖池状态给玩家#{user_id}: #{prize_status.current_amount}")
  end

  @doc """
  处理玩家倍率切换时的PRIZE奖池逻辑

  **调用时机**：
  - 玩家在游戏中切换下注倍率时
  - 确保PRIZE奖池金额不受倍率切换影响
  """
  def handle_multiplier_change_for_player(state, user_id, old_multiplier, new_multiplier) do
    {:ok, numeric_id} = get_numeric_id(user_id)

    # 🎯 处理PRIZE奖池的倍率切换逻辑
    current_prize_pool = state.game_data.prize_pool

    {unchanged_pool, multiplier_info} =
      SlotNiuLogic.handle_multiplier_change(old_multiplier, new_multiplier, current_prize_pool)

    # 更新游戏数据
    new_game_data = %{
      state.game_data
      | # 确保奖池金额不变
        prize_pool: unchanged_pool
    }

    updated_state = %{state | game_data: new_game_data}

    Logger.info(
      "🎰 [MULTIPLIER_CHANGE] 玩家#{user_id}(#{numeric_id})倍率变化: #{old_multiplier} → #{new_multiplier}"
    )

    Logger.info(
      "🎰 [MULTIPLIER_CHANGE] PRIZE奖池保持不变: #{unchanged_pool}, 收集速度: #{multiplier_info.speed_change}"
    )

    # 🎯 发送PRIZE奖池状态更新给玩家
    prize_status = SlotNiuLogic.get_prize_pool_status(unchanged_pool, new_multiplier)
    send_prize_status_to_player(updated_state, user_id, prize_status)

    updated_state
  end

  # ==================== 奖池管理函数 ====================

  # 初始化奖池
  defp init_jackpots(state, merged_config) do
    # 从合并后的配置中获取奖池配置
    jackpot_config = merged_config.jackpot
    jackpot_pools = Map.get(jackpot_config, :pools, [])

    case JackpotManager.init_game_jackpots(state.game_id, jackpot_pools) do
      {:ok, jackpot_ids} ->
        Logger.info("✅ [SLOTNIU] 奖池初始化成功: #{inspect(jackpot_ids)}")

      {:error, errors} ->
        Logger.error("❌ [SLOTNIU] 奖池初始化失败: #{inspect(errors)}")
    end

    state
  end

  # 深度合并配置，确保所有配置项都有默认值兜底
  defp deep_merge_config(default_config, room_config)
       when is_map(default_config) and is_map(room_config) do
    Map.merge(default_config, room_config, fn
      _key, default_value, room_value when is_map(default_value) and is_map(room_value) ->
        deep_merge_config(default_value, room_value)

      _key, _default_value, room_value ->
        room_value
    end)
  end

  defp deep_merge_config(default_config, _room_config), do: default_config

  # 实现奖池更新回调
  def on_jackpot_updated(state, jackpot_id, new_balance) do
    Logger.info("💰 [SLOTNIU] 奖池更新: #{jackpot_id} -> #{new_balance}")

    # 更新本地奖池数据
    updated_state =
      case jackpot_id do
        :jackpot ->
          new_game_data = %{state.game_data | jackpot_amount: new_balance}
          %{state | game_data: new_game_data}

        :prize ->
          new_game_data = %{state.game_data | prize_amount: new_balance}
          %{state | game_data: new_game_data}

        _ ->
          state
      end

    # 广播奖池更新给所有玩家
    broadcast_jackpot_update(updated_state, jackpot_id, new_balance)

    updated_state
  end

  # 广播奖池更新
  defp broadcast_jackpot_update(state, jackpot_id, new_balance) do
    update_data = %{
      "jackpot_id" => jackpot_id,
      "jackpot_amount" => new_balance,
      "updated_at" => DateTime.utc_now() |> DateTime.to_unix()
    }

    broadcast_to_room(state, %{
      # Game protocol
      "mainId" => 5,
      # Jackpot update protocol
      "subId" => 1010,
      "data" => update_data
    })
  end

  # 处理奖池贡献（使用真实奖池系统）
  defp handle_jackpot_contribution(state, player, bet_amount) do
    # 从 JackpotManager 获取贡献率配置
    contribution_rate = JackpotManager.get_total_contribution_rate(state.game_id)
    Logger.info("🎰 [SLOTNIU] 处理奖池贡献 - 下注金额: #{bet_amount}, 贡献率: #{contribution_rate} (来源: 后台配置)")

    if contribution_rate > 0 and not player.is_robot do
      total_contribution = JackpotManager.calculate_contribution(bet_amount, contribution_rate)

      if total_contribution > 0 do
        # 全部贡献给主奖池
        case contribute_to_jackpot(state, :jackpot, total_contribution,
               description: "SlotNiu 奖池贡献",
               metadata: %{
                 game_type: :slotniu,
                 player_id: player.user_id,
                 bet_amount: bet_amount,
                 contribution_rate: contribution_rate,
                 contribution_time: DateTime.utc_now()
               }
             ) do
          {:ok, _transfer} ->
            Logger.info("💰 [SLOTNIU] 奖池贡献成功: 玩家#{player.user_id}, 金额#{total_contribution}")

          {:error, reason} ->
            Logger.error("❌ [SLOTNIU] 奖池贡献失败: 玩家#{player.user_id}, 原因#{inspect(reason)}")
        end
      end
    end

    state
  end

  # 获取玩家信息的辅助函数
  defp get_player_by_user_id(state, user_id) do
    Enum.find_value(state.players, fn {_numeric_id, player} ->
      if player.user_id == user_id, do: player, else: nil
    end)
  end

  # 处理奖池中奖（使用统一API）
  defp handle_jackpot_win_unified(state, player, jackpot_type, jackpot_amount) do
    if jackpot_amount > 0 do
      case Cypridina.Ledger.jackpot_win(
             state.game_id,
             jackpot_type,
             player.user_id,
             jackpot_amount,
             description: "SlotNiu #{jackpot_type} 中奖",
             metadata: %{
               game_type: :slotniu,
               player_id: player.user_id,
               jackpot_type: jackpot_type,
               jackpot_amount: jackpot_amount,
               win_time: DateTime.utc_now()
             }
           ) do
        {:ok, _transfer} ->
          Logger.info(
            "🎉 [SLOTNIU] #{jackpot_type}中奖支付成功: 玩家#{player.user_id}, 金额#{jackpot_amount}"
          )

          # 更新玩家积分显示
          updated_state = add_player_points(state, player.numeric_id, jackpot_amount)

          # 如果是Prize奖池中奖，重置Prize奖池
          if jackpot_type == :prize do
            case JackpotManager.reset_jackpot(state.game_id, :prize, 0) do
              {:ok, _} ->
                Logger.info("🔄 [SLOTNIU] Prize奖池已重置")

              {:error, reason} ->
                Logger.error("❌ [SLOTNIU] Prize奖池重置失败: #{inspect(reason)}")
            end
          end

          updated_state

        {:error, reason} ->
          Logger.error("❌ [SLOTNIU] #{jackpot_type}中奖支付失败: #{inspect(reason)}")
          state
      end
    else
      state
    end
  end

  # 格式化时间戳为SlotNiu前端期望的格式 (MM-dd HH:mm)
  # 按照slotniu_jackpot.ex中format_timestamp_for_frontend的逻辑
  defp format_timestamp_for_slotniu_frontend(unix_timestamp) when is_integer(unix_timestamp) do
    case DateTime.from_unix(unix_timestamp) do
      {:ok, datetime} ->
        # 转换为UTC+8时区并格式化为 "MM-dd HH:mm" 格式
        datetime
        # 转换为UTC+8
        |> DateTime.add(8 * 3600, :second)
        |> DateTime.to_string()
        # 提取 "MM-dd HH:mm" 部分
        |> String.slice(5, 11)
        # 将T替换为空格
        |> String.replace("T", " ")

      {:error, _} ->
        "00-00 00:00"
    end
  end

  defp format_timestamp_for_slotniu_frontend(_), do: "00-00 00:00"

  # 从配置中计算Jackpot倍数
  # 使用与slotniu_jackpot.ex中calculate_correct_multiplier相同的逻辑
  defp calculate_jackpot_multiplier_from_config(bet_amount, niunum) do
    try do
      config = Cypridina.Teen.GameSystem.Games.SlotNiu.SlotNiuConfig.get_default_config()
      jackpot_multipliers = get_in(config, [:jackpot, :multipliers])

      # 查找匹配的配置
      bet_config = Map.get(jackpot_multipliers, bet_amount)

      if bet_config do
        Map.get(bet_config, niunum, 1)
      else
        # 如果没有直接匹配，按比例计算（基于900的配置）
        base_config = Map.get(jackpot_multipliers, 900, %{})
        base_mult = Map.get(base_config, niunum, 1)

        if base_mult > 1 and bet_amount > 0 do
          # 按比例计算倍率
          calculated_mult = base_mult * bet_amount / 900
          # 保留两位小数
          Float.round(calculated_mult, 2)
        else
          1
        end
      end
    rescue
      _error ->
        # 出错时返回默认倍数
        1
    end
  end
end
