defmodule Teen.GameSystem.JackpotManager do
  @moduledoc """
  奖池管理服务

  专注于系统管理和工具函数，避免与 RoomBase 功能重叠：
  - 奖池初始化和重置（系统管理）
  - 批量操作和状态检查（管理界面）
  - 工具函数（计算、分配、验证）
  - ID标准化（历史兼容）
  - 贡献率配置管理（统一缓存）

  ## 使用指南

  **游戏运行时** - 使用 RoomBase 方法（有 state 上下文）：
  ```elixir
  # ✅ 正确：使用 RoomBase 方法
  contribution_rate = JackpotManager.get_total_contribution_rate(game_id)  # 获取贡献率
  contribution = JackpotManager.calculate_contribution(bet, contribution_rate)  # 工具函数
  contribute_to_jackpot(state, :main, contribution)  # RoomBase 方法
  balance = get_jackpot_balance(state, :main)  # RoomBase 方法
  ```

  **系统管理** - 使用 JackpotManager 方法（无 state 依赖）：
  ```elixir
  # ✅ 正确：使用 JackpotManager 管理功能
  JackpotManager.init_game_jackpots(40, configs)  # 初始化
  JackpotManager.reset_jackpot(40, :main, 250000)  # 重置
  JackpotManager.get_game_jackpots_balance(40, [:main, :left])  # 批量查询
  JackpotManager.refresh_contribution_rates(40)  # 刷新贡献率配置
  ```
  """

  require Logger
  alias Cypridina.Ledger
  alias Cypridina.Ledger.{AccountIdentifier, BalanceCache}
  alias Teen.GameManagement.JackpotConfig

  # ETS 表名
  @contribution_cache_table :jackpot_contribution_cache

  # 默认贡献率配置
  @default_contribution_rates %{
    # Slot777
    "40" => 0.02,
    # SlotNiu
    "41" => 0.02,
    # SlotCat
    "42" => 0.02
  }

  # 初始化贡献率缓存表（私有函数，懒加载）
  defp init_contribution_cache do
    case :ets.whereis(@contribution_cache_table) do
      :undefined ->
        :ets.new(@contribution_cache_table, [:set, :public, :named_table])
        Logger.info("✅ [JACKPOT_MANAGER] 贡献率缓存表初始化成功")
        :ok

      _table ->
        Logger.info("ℹ️ [JACKPOT_MANAGER] 贡献率缓存表已存在")
        :ok
    end
  end

  @doc """
  获取游戏总贡献率（适用于单奖池游戏如 Slot777、SlotNiu）

  ## 参数
  - `game_id`: 游戏ID (支持数字或字符串)

  ## 返回
  - 贡献率（小数形式，如 0.02 表示 2%）

  ## 示例
      rate = JackpotManager.get_total_contribution_rate(40)  # 0.02
      rate = JackpotManager.get_total_contribution_rate("40")  # 0.02
  """
  def get_total_contribution_rate(game_id) do
    string_game_id = normalize_game_id(game_id)

    case get_from_cache({string_game_id, :total}) do
      {:ok, rate} ->
        rate

      {:error, :not_found} ->
        # 缓存未命中，从数据库加载
        case load_contribution_rates_from_db(string_game_id) do
          {:ok, _rates} ->
            # 重新从缓存获取
            case get_from_cache({string_game_id, :total}) do
              {:ok, rate} -> rate
              {:error, :not_found} -> get_default_contribution_rate(string_game_id)
            end

          {:error, _reason} ->
            # 数据库查询失败，使用默认值
            get_default_contribution_rate(string_game_id)
        end
    end
  end

  @doc """
  获取游戏各奖池贡献率映射（适用于多奖池游戏如 SlotCat）

  ## 参数
  - `game_id`: 游戏ID (支持数字或字符串)

  ## 返回
  - 奖池贡献率映射 %{jackpot_id => contribution_rate}

  ## 示例
      rates = JackpotManager.get_jackpot_contribution_rates(42)
      # %{:left => 0.006, :right => 0.008, :center => 0.006}
  """
  def get_jackpot_contribution_rates(game_id) do
    string_game_id = normalize_game_id(game_id)

    case get_from_cache({string_game_id, :jackpot_rates}) do
      {:ok, rates} ->
        rates

      {:error, :not_found} ->
        # 缓存未命中，从数据库加载
        case load_contribution_rates_from_db(string_game_id) do
          {:ok, _rates} ->
            # 重新从缓存获取
            case get_from_cache({string_game_id, :jackpot_rates}) do
              {:ok, rates} -> rates
              {:error, :not_found} -> %{}
            end

          {:error, _reason} ->
            # 数据库查询失败，返回空映射
            %{}
        end
    end
  end

  @doc """
  获取特定奖池的贡献率

  ## 参数
  - `game_id`: 游戏ID (支持数字或字符串)
  - `jackpot_id`: 奖池ID (原子或字符串)

  ## 返回
  - 贡献率（小数形式）
  """
  def get_jackpot_contribution_rate(game_id, jackpot_id) do
    rates = get_jackpot_contribution_rates(game_id)
    jackpot_atom = if is_binary(jackpot_id), do: String.to_atom(jackpot_id), else: jackpot_id
    Map.get(rates, jackpot_atom, 0.0)
  end

  @doc """
  初始化游戏的所有奖池

  ## 参数
  - `game_id`: 游戏ID (支持数字或字符串)
  - `jackpot_configs`: 奖池配置列表

  ## 示例
      jackpot_configs = [
        %{identifier: :main, base_amount: 1000000},
        %{identifier: :left, base_amount: 625000},
        %{identifier: :right, base_amount: 1250000},
        %{identifier: :center, base_amount: 2500000}
      ]
      JackpotManager.init_game_jackpots(40, jackpot_configs)  # 数字ID
      JackpotManager.init_game_jackpots("40", jackpot_configs)  # 字符串ID
  """
  def init_game_jackpots(game_id, jackpot_configs) when is_list(jackpot_configs) do
    # 统一转换为字符串ID
    string_game_id = normalize_game_id(game_id)
    Logger.info("🎰 [JACKPOT_MANAGER] 初始化游戏 #{string_game_id} (原始: #{game_id}) 的奖池")

    results =
      Enum.map(jackpot_configs, fn config ->
        jackpot_id = config.identifier
        base_amount = config.base_amount

        case init_single_jackpot(string_game_id, jackpot_id, base_amount) do
          {:ok, _transfer} ->
            Logger.info(
              "✅ [JACKPOT_MANAGER] 奖池 #{game_id}:#{jackpot_id} 初始化成功，底金: #{base_amount}"
            )

            {:ok, jackpot_id}

          {:error, reason} ->
            Logger.error(
              "❌ [JACKPOT_MANAGER] 奖池 #{game_id}:#{jackpot_id} 初始化失败: #{inspect(reason)}"
            )

            {:error, {jackpot_id, reason}}
        end
      end)

    # 检查是否有失败的初始化
    failed = Enum.filter(results, &match?({:error, _}, &1))

    if Enum.empty?(failed) do
      {:ok, Enum.map(results, fn {:ok, jackpot_id} -> jackpot_id end)}
    else
      {:error, failed}
    end
  end

  @doc """
  初始化单个奖池
  """
  def init_single_jackpot(game_id, jackpot_id, base_amount) do
    # 统一转换为字符串ID
    string_game_id = normalize_game_id(game_id)
    # 检查奖池是否已经存在且有余额
    current_balance = get_jackpot_balance_direct(string_game_id, jackpot_id)

    if current_balance > 0 do
      Logger.info("ℹ️ [JACKPOT_MANAGER] 奖池 #{game_id}:#{jackpot_id} 已存在，当前余额: #{current_balance}")
      {:ok, :already_exists}
    else
      # 初始化奖池底金
      Ledger.init_jackpot(game_id, jackpot_id, base_amount,
        description: "初始化奖池底金",
        metadata: %{
          game_id: game_id,
          jackpot_id: jackpot_id,
          base_amount: base_amount,
          init_time: DateTime.utc_now()
        }
      )
    end
  end

  @doc """
  重置奖池到底金金额
  """
  def reset_jackpot(game_id, jackpot_id, base_amount) do
    string_game_id = normalize_game_id(game_id)
    current_balance = get_jackpot_balance_direct(string_game_id, jackpot_id)

    cond do
      current_balance == base_amount ->
        Logger.info("ℹ️ [JACKPOT_MANAGER] 奖池 #{game_id}:#{jackpot_id} 已经是底金金额: #{base_amount}")
        {:ok, :no_change}

      current_balance > base_amount ->
        # 扣除多余金额到系统账户
        excess_amount = current_balance - base_amount
        jackpot_identifier = AccountIdentifier.jackpot(game_id, jackpot_id, :XAA)
        system_identifier = AccountIdentifier.system(:jackpot_fund, :XAA)

        Ledger.transfer(jackpot_identifier, system_identifier, excess_amount,
          description: "重置奖池，回收多余金额",
          metadata: %{
            game_id: game_id,
            jackpot_id: jackpot_id,
            excess_amount: excess_amount,
            reset_time: DateTime.utc_now()
          }
        )

      current_balance < base_amount ->
        # 补充不足金额
        shortage_amount = base_amount - current_balance

        Ledger.init_jackpot(game_id, jackpot_id, shortage_amount,
          description: "重置奖池，补充不足金额",
          metadata: %{
            game_id: game_id,
            jackpot_id: jackpot_id,
            shortage_amount: shortage_amount,
            reset_time: DateTime.utc_now()
          }
        )
    end
  end

  @doc """
  获取奖池余额（用于后台管理界面和API）

  ⚠️ 注意：游戏运行时应使用 RoomBase.get_jackpot_balance(state, jackpot_id)
  这个函数主要用于：
  - 后台管理界面显示实时余额
  - API接口返回奖池状态
  - 系统管理功能（初始化、重置等）
  """
  def get_jackpot_balance(game_id, jackpot_id) do
    string_game_id = normalize_game_id(game_id)
    jackpot_identifier = AccountIdentifier.jackpot(string_game_id, jackpot_id, :XAA)

    case BalanceCache.get_balance(jackpot_identifier) do
      {:ok, balance} -> balance
      {:error, _reason} -> 0
    end
  end

  # 内部函数：直接获取奖池余额（避免重复代码）
  defp get_jackpot_balance_direct(game_id, jackpot_id) do
    get_jackpot_balance(game_id, jackpot_id)
  end

  @doc """
  获取游戏的所有奖池余额（批量操作，用于管理界面）
  """
  def get_game_jackpots_balance(game_id, jackpot_ids) when is_list(jackpot_ids) do
    Enum.map(jackpot_ids, fn jackpot_id ->
      balance = get_jackpot_balance_direct(game_id, jackpot_id)
      %{jackpot_id: jackpot_id, balance: balance}
    end)
  end

  @doc """
  检查奖池状态
  """
  def check_jackpot_status(game_id, jackpot_id, min_amount \\ 0) do
    balance = get_jackpot_balance_direct(game_id, jackpot_id)

    status =
      cond do
        balance >= min_amount -> :healthy
        balance > 0 -> :low
        true -> :empty
      end

    %{
      game_id: game_id,
      jackpot_id: jackpot_id,
      balance: balance,
      min_amount: min_amount,
      status: status,
      checked_at: DateTime.utc_now()
    }
  end

  @doc """
  批量检查游戏奖池状态
  """
  def check_game_jackpots_status(game_id, jackpot_configs) when is_list(jackpot_configs) do
    Enum.map(jackpot_configs, fn config ->
      jackpot_id = config.identifier
      min_amount = Map.get(config, :min_amount, 0)
      check_jackpot_status(game_id, jackpot_id, min_amount)
    end)
  end

  @doc """
  计算奖池贡献金额
  """
  def calculate_contribution(bet_amount, contribution_rate)
      when is_number(bet_amount) and is_number(contribution_rate) do
    trunc(bet_amount * contribution_rate)
  end

  @doc """
  分配奖池贡献到多个奖池（按权重）
  """
  def distribute_contribution(total_contribution, pool_weights) when is_map(pool_weights) do
    total_weight = Enum.sum(Map.values(pool_weights))

    if total_weight == 0 do
      %{}
    else
      Enum.into(pool_weights, %{}, fn {pool_id, weight} ->
        contribution = trunc(total_contribution * weight / total_weight)
        {pool_id, contribution}
      end)
    end
  end

  @doc """
  获取奖池标识符
  """
  def get_jackpot_identifier(game_id, jackpot_id) do
    AccountIdentifier.jackpot(game_id, jackpot_id, :XAA)
  end

  @doc """
  验证奖池配置
  """
  def validate_jackpot_config(config) when is_map(config) do
    required_fields = [:identifier, :base_amount]

    missing_fields =
      Enum.filter(required_fields, fn field ->
        not Map.has_key?(config, field)
      end)

    if Enum.empty?(missing_fields) do
      {:ok, config}
    else
      {:error, {:missing_fields, missing_fields}}
    end
  end

  def validate_jackpot_configs(configs) when is_list(configs) do
    results = Enum.map(configs, &validate_jackpot_config/1)

    errors = Enum.filter(results, &match?({:error, _}, &1))

    if Enum.empty?(errors) do
      {:ok, configs}
    else
      {:error, errors}
    end
  end

  @doc """
  刷新特定游戏的贡献率配置缓存

  ## 参数
  - `game_id`: 游戏ID (支持数字或字符串)

  ## 示例
      JackpotManager.refresh_contribution_rates(40)
  """
  def refresh_contribution_rates(game_id) do
    string_game_id = normalize_game_id(game_id)

    case load_contribution_rates_from_db(string_game_id) do
      {:ok, _rates} ->
        Logger.info("✅ [JACKPOT_MANAGER] 游戏 #{string_game_id} 贡献率配置刷新成功")
        :ok

      {:error, reason} ->
        Logger.error("❌ [JACKPOT_MANAGER] 游戏 #{string_game_id} 贡献率配置刷新失败: #{inspect(reason)}")
        {:error, reason}
    end
  end

  @doc """
  刷新所有游戏的贡献率配置缓存
  """
  def refresh_all_contribution_rates do
    # Slot777, SlotNiu, SlotCat
    game_ids = ["40", "41", "42"]

    results =
      Enum.map(game_ids, fn game_id ->
        {game_id, refresh_contribution_rates(game_id)}
      end)

    failed = Enum.filter(results, fn {_game_id, result} -> match?({:error, _}, result) end)

    if Enum.empty?(failed) do
      Logger.info("✅ [JACKPOT_MANAGER] 所有游戏贡献率配置刷新成功")
      :ok
    else
      Logger.error("❌ [JACKPOT_MANAGER] 部分游戏贡献率配置刷新失败: #{inspect(failed)}")
      {:error, failed}
    end
  end

  @doc """
  将游戏ID统一转换为字符串格式

  支持数字ID转换为字符串，确保数据库存储的一致性
  """
  def normalize_game_id(game_id) when is_integer(game_id), do: Integer.to_string(game_id)
  def normalize_game_id(game_id) when is_binary(game_id), do: game_id
  def normalize_game_id(game_id), do: to_string(game_id)

  # ==================== 私有函数 ====================

  # 从缓存获取数据
  defp get_from_cache(key) do
    # 确保缓存表存在
    init_contribution_cache()

    case :ets.lookup(@contribution_cache_table, key) do
      [{^key, value}] -> {:ok, value}
      [] -> {:error, :not_found}
    end
  end

  # 存储数据到缓存
  defp put_to_cache(key, value) do
    # 确保缓存表存在
    init_contribution_cache()
    :ets.insert(@contribution_cache_table, {key, value})
  end

  # 从数据库加载贡献率配置
  defp load_contribution_rates_from_db(game_id) do
    try do
      configs = JackpotConfig.list_by_game!(%{game_id: game_id})

      if Enum.empty?(configs) do
        # 数据库中没有配置，使用默认值
        default_rate = get_default_contribution_rate(game_id)
        put_to_cache({game_id, :total}, default_rate)
        put_to_cache({game_id, :jackpot_rates}, %{})
        put_to_cache({game_id, :last_updated}, DateTime.utc_now())

        Logger.info("ℹ️ [JACKPOT_MANAGER] 游戏 #{game_id} 使用默认贡献率: #{default_rate}")
        {:ok, %{total: default_rate, jackpot_rates: %{}}}
      else
        # 计算总贡献率和各奖池贡献率
        total_rate = configs |> Enum.map(&Decimal.to_float(&1.contribution_rate)) |> Enum.sum()

        jackpot_rates =
          configs
          |> Enum.map(fn config ->
            jackpot_id = String.to_atom(config.jackpot_id)
            rate = Decimal.to_float(config.contribution_rate)
            {jackpot_id, rate}
          end)
          |> Enum.into(%{})

        # 存储到缓存
        put_to_cache({game_id, :total}, total_rate)
        put_to_cache({game_id, :jackpot_rates}, jackpot_rates)
        put_to_cache({game_id, :last_updated}, DateTime.utc_now())

        Logger.info(
          "✅ [JACKPOT_MANAGER] 游戏 #{game_id} 贡献率配置加载成功 - 总计: #{total_rate}, 奖池: #{inspect(jackpot_rates)}"
        )

        {:ok, %{total: total_rate, jackpot_rates: jackpot_rates}}
      end
    rescue
      error ->
        Logger.error("❌ [JACKPOT_MANAGER] 游戏 #{game_id} 贡献率配置加载失败: #{inspect(error)}")
        {:error, error}
    end
  end

  # 获取默认贡献率
  defp get_default_contribution_rate(game_id) do
    Map.get(@default_contribution_rates, game_id, 0.02)
  end

  # ==================== Jackpot记录从流水表查询方法 ====================

  # 注意：Jackpot记录现在直接存储在转账流水表(ledger_transfers)的metadata中
  # 通过 Ledger.jackpot_win() 调用时自动创建，无需单独的表和创建函数

  @doc """
  按游戏类型获取最近的Jackpot记录（从流水表查询）

  ## 参数
  - `game_type`: 游戏类型
  - `opts`: 选项
    - `limit`: 记录数量限制 (默认 10)

  ## 示例
      JackpotManager.get_recent_jackpot_records("slot777", limit: 20)
  """
  def get_recent_jackpot_records(game_type, opts \\ []) do
    limit = Keyword.get(opts, :limit, 10)

    # 使用原生SQL从流水表查询
    query = """
    SELECT 
      id,
      amount,
      description,
      metadata,
      timestamp
    FROM ledger_transfers 
    WHERE transaction_type = 'jackpot_win' 
      AND metadata->>'game_type' = $1
    ORDER BY timestamp DESC 
    LIMIT $2
    """

    case Cypridina.Repo.query(query, [game_type, limit]) do
      {:ok, %{rows: rows}} ->
        records = Enum.map(rows, &format_transfer_to_record/1)

        Logger.debug(
          "🔍 [JACKPOT_MANAGER] 获取最近Jackpot记录成功 - 游戏: #{game_type}, 数量: #{length(records)}"
        )

        {:ok, records}

      {:error, reason} ->
        Logger.error(
          "❌ [JACKPOT_MANAGER] 获取最近Jackpot记录失败 - 游戏: #{game_type}, 原因: #{inspect(reason)}"
        )

        {:error, reason}
    end
  end

  @doc """
  按游戏类型获取最高中奖记录（从流水表查询）

  ## 参数  
  - `game_type`: 游戏类型
  - `opts`: 选项
    - `limit`: 记录数量限制 (默认 20)
    - `min_amount`: 最小中奖金额过滤 (默认 0)

  ## 示例
      JackpotManager.get_top_jackpot_records("slot777", limit: 50, min_amount: 100000)
  """
  def get_top_jackpot_records(game_type, opts \\ []) do
    limit = Keyword.get(opts, :limit, 20)
    min_amount = Keyword.get(opts, :min_amount, 0)

    # 从流水表按金额排序查询
    query = """
    SELECT 
      id,
      amount,
      description,
      metadata,
      timestamp
    FROM ledger_transfers 
    WHERE transaction_type = 'jackpot_win' 
      AND metadata->>'game_type' = $1
      AND (metadata->>'jackpot_amount')::integer >= $2
    ORDER BY (metadata->>'jackpot_amount')::integer DESC, timestamp DESC
    LIMIT $3
    """

    case Cypridina.Repo.query(query, [game_type, min_amount, limit]) do
      {:ok, %{rows: rows}} ->
        records = Enum.map(rows, &format_transfer_to_record/1)

        Logger.debug(
          "🔍 [JACKPOT_MANAGER] 获取最高Jackpot记录成功 - 游戏: #{game_type}, 数量: #{length(records)}"
        )

        {:ok, records}

      {:error, reason} ->
        Logger.error(
          "❌ [JACKPOT_MANAGER] 获取最高Jackpot记录失败 - 游戏: #{game_type}, 原因: #{inspect(reason)}"
        )

        {:error, reason}
    end
  end

  @doc """
  获取用于前端展示的游戏记录（游戏协议使用，从流水表查询）

  ## 参数
  - `game_type`: 游戏类型
  - `limit`: 记录数量限制 (默认 10)

  ## 示例
      JackpotManager.get_display_records("slot777", 15)
  """
  def get_display_records(game_type, limit \\ 10) do
    case get_recent_jackpot_records(game_type, limit: limit) do
      {:ok, records} ->
        # 转换为游戏协议需要的格式
        display_records = Enum.map(records, &format_record_for_display/1)

        Logger.debug(
          "🔍 [JACKPOT_MANAGER] 获取展示记录成功 - 游戏: #{game_type}, 数量: #{length(display_records)}"
        )

        {:ok, display_records}

      {:error, reason} ->
        Logger.error("❌ [JACKPOT_MANAGER] 获取展示记录失败 - 游戏: #{game_type}, 原因: #{inspect(reason)}")
        {:error, reason}
    end
  end

  # ==================== 私有辅助函数 ====================

  # 将流水记录格式化为统一的记录格式
  defp format_transfer_to_record([id, amount_tuple, description, metadata, timestamp]) do
    # 解析金额
    {_currency, amount_decimal} = amount_tuple
    jackpot_amount = Decimal.to_integer(amount_decimal)

    # 解析时间戳
    win_time =
      case timestamp do
        %NaiveDateTime{} = naive -> DateTime.from_naive!(naive, "Etc/UTC")
        %DateTime{} = dt -> dt
        _ -> DateTime.utc_now()
      end

    # 从metadata中提取信息，提供默认值
    %{
      id: id,
      game_type: Map.get(metadata, "game_type", "unknown"),
      user_id: Map.get(metadata, "user_id"),
      player_id: Map.get(metadata, "player_id"),
      player_name: Map.get(metadata, "player_name", "未知玩家"),
      avatar_id: Map.get(metadata, "avatar_id", 1),
      avatar_url: Map.get(metadata, "avatar_url", ""),
      jackpot_amount: jackpot_amount,
      bet_amount: Map.get(metadata, "bet_amount", 0),
      jackpot_level: Map.get(metadata, "jackpot_level", 1),
      game_specific_data: Map.get(metadata, "game_specific_data", %{}),
      win_time: win_time,
      description: description
    }
  end

  # 将记录格式化为前端展示格式
  defp format_record_for_display(record) do
    %{
      "player_id" => record.player_id,
      "player_name" => record.player_name,
      "avatar_id" => record.avatar_id,
      "avatar_url" => record.avatar_url || "",
      "win_amount" => record.jackpot_amount,
      "bet_amount" => record.bet_amount,
      "jackpot_level" => record.jackpot_level,
      "win_time" => DateTime.to_unix(record.win_time),
      "game_data" => record.game_specific_data,
      # 流水表中的都是真实记录
      "is_fake" => false
    }
  end
end
