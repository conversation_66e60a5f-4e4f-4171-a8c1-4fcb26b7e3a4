defmodule Teen.Live.UserLive do
  @moduledoc """
  用户管理页面

  提供用户的创建、查看、编辑和管理功能
  """

  use AshBackpex.LiveResource

  import Phoenix.Component
  import Phoenix.LiveView

  backpex do
    resource Cypridina.Accounts.User
    layout({Teen.Layouts, :admin})

    @impl Backpex.LiveResource
    def singular_name, do: "用户"

    @impl Backpex.LiveResource
    def plural_name, do: "用户列表"
    # 确保用户列表可以被其他模块引用
    @impl Backpex.LiveResource
    def mount(_params, _session, socket) do
      socket = assign(socket, :fluid?, true)
      {:ok, socket}
    end

    fields do
      field :numeric_id do
        module Backpex.Fields.Number
        label("玩家ID")
        only([:index, :show])
      end

      field :username do
        module Backpex.Fields.Text
        label("用户名")
        searchable(true)
        help_text("3-20个字符，只能包含字母、数字和下划线")
      end

      # 密码字段 - 仅在创建时显示
      field :password do
        module Backpex.Fields.Text
        label("密码")
        only([:new])
        help_text("密码长度至少6位")
      end

      # 确认密码字段 - 仅在创建时显示
      field :password_confirmation do
        module Backpex.Fields.Text
        label("确认密码")
        only([:new])
        help_text("请再次输入密码")
      end

      field :email do
        module Backpex.Fields.Text
        label("邮箱")
        searchable(true)
      end

      field :phone do
        module Backpex.Fields.Text
        label("手机号")
        searchable(true)
      end

      field :permission_level do
        module Backpex.Fields.Select
        label("权限级别")

        options([
          {"普通用户", 0},
          {"管理员", 1},
          {"超级管理员", 2}
        ])
      end

      field :agent_level do
        module Backpex.Fields.Number
        label("代理等级")
        help_text("-1=不是代理，0=根代理，>0=下级代理")
        default fn _assigns -> -1 end
      end

      # UserProfile 相关字段
      field :nickname do
        module Backpex.Fields.Text
        label("昵称")
        only([:new, :edit])
        help_text("用户显示昵称，默认使用用户名")
      end

      field :gender do
        module Backpex.Fields.Select
        label("性别")
        only([:new, :edit])

        options([
          {"未知", 0},
          {"男", 1},
          {"女", 2}
        ])

        default fn _assigns -> 0 end
      end

      field :avatar_url do
        module Backpex.Fields.Text
        label("头像URL")
        only([:new, :edit])
        help_text("自定义头像链接")
      end

      field :head_id do
        module Backpex.Fields.Number
        label("预设头像ID")
        only([:new, :edit])
        help_text("客户端预设头像ID（1-999）")
        default fn _assigns -> 1 end
      end

      field :confirmed_at do
        module Backpex.Fields.DateTime
        label("邮箱确认时间")
        only([:show])
      end

      field :phone_verified_at do
        module Backpex.Fields.DateTime
        label("手机验证时间")
        only([:show])
      end

      field :inserted_at do
        module Backpex.Fields.DateTime
        label("创建时间")
        only([:index, :show])
      end

      field :updated_at do
        module Backpex.Fields.DateTime
        label("更新时间")
        only([:show])
      end
    end
  end

  @impl Backpex.LiveResource
  def can?(assigns, action, item) do
    # 检查当前用户的权限级别
    case assigns.current_user do
      %{permission_level: level} when level >= 2 ->
        # 超级管理员可以执行所有操作
        true
      %{permission_level: 1} ->
        # 管理员可以查看、编辑，但不能删除用户
        action in [:index, :show, :new, :edit]
      %{permission_level: 0} ->
        # 普通用户只能查看列表和详情
        action in [:index, :show]
      _ ->
        # 未登录或无权限用户
        false
    end
  end

  @impl Backpex.LiveResource
  def return_to(_socket, _action, _item, _live_action, _params) do
    "/admin/users"
  end
end
