defmodule Teen.Live.ActivitySystem.CdkeyClaimRecordLive do
  @moduledoc """
  CDKEY领取记录管理页面
  """

  use AshBackpex.LiveResource

  import Phoenix.Component
  import Phoenix.LiveView

  backpex do
    resource Teen.ActivitySystem.CdkeyClaimRecord
    layout({Teen.Layouts, :admin})

    fields do
      field :cdkey do
        module Backpex.Fields.BelongsTo
        label("CDKEY")
        display_field(:code)
        orderable(true)
        searchable(true)
      end

      field :user_id do
        module Backpex.Fields.Text
        label("用户ID")
        searchable(true)

        render(fn assigns ->
          ~H"""
          <span class="font-mono text-sm">{String.slice(assigns.value || "", 0, 8)}...</span>
          """
        end)
      end

      field :username do
        module Backpex.Fields.Text
        label("用户名")
        searchable(true)
        orderable(true)
      end

      field :claimed_rewards do
        module Backpex.Fields.Textarea
        label("领取奖励")

        render(fn assigns ->
          rewards = assigns.value || %{}
          formatted = Jason.encode!(rewards, pretty: true)

          ~H"""
          <pre class="text-xs bg-gray-100 p-2 rounded max-w-xs overflow-auto"><%= formatted %></pre>
          """
        end)
      end

      field :claimed_at do
        module Backpex.Fields.DateTime
        label("领取时间")
        orderable(true)
      end

      field :ip_address do
        module Backpex.Fields.Text
        label("IP地址")
        searchable(true)
      end

      field :inserted_at do
        module Backpex.Fields.DateTime
        label("记录时间")
        orderable(true)
        readonly(true)
      end
    end

    filters do
      filter :cdkey_code do
        module __MODULE__.CdkeyCodeFilter
        label("CDKEY代码")
      end

      filter :username do
        module __MODULE__.UsernameFilter
        label("用户名")
      end

      filter :ip_address do
        module __MODULE__.IpAddressFilter
        label("IP地址")
      end
    end
  end

  @impl Backpex.LiveResource
  def singular_name, do: "CDKEY领取记录"

  @impl Backpex.LiveResource
  def plural_name, do: "CDKEY领取记录"

  @impl Backpex.LiveResource
  def item_actions(_) do
    [
      show: %{
        module: Backpex.ItemActions.Show,
        only: [:row]
      },
      edit: %{
        module: Backpex.ItemActions.Edit,
        only: [:row, :show]
      },
      delete: %{
        module: Backpex.ItemActions.Delete,
        only: [:row, :show]
      },
      export: %{
        module: __MODULE__.ExportAction,
        only: [:index]
      },
      statistics: %{
        module: __MODULE__.StatisticsAction,
        only: [:index]
      }
    ]
  end

  # CDKEY代码过滤器
  defmodule CdkeyCodeFilter do
    use Backpex.Filter
    import Ash.Expr
    import Phoenix.Component
    import CypridinaWeb.CoreComponents

    @impl Backpex.Filter
    def label, do: "CDKEY代码"

    @impl Backpex.Filter
    def render_form(assigns) do
      ~H"""
      <.input field={@form[:value]} type="text" placeholder="输入CDKEY代码" />
      """
    end

    @impl Backpex.Filter
    def filter(query, %{"value" => value}) when value != "" do
      # 通过关联查询过滤
      Ash.Query.filter(query, expr(cdkey.code == ^value))
    end

    def filter(query, _params), do: query
  end

  # 用户名过滤器
  defmodule UsernameFilter do
    use Backpex.Filter
    import Ash.Expr
    import Phoenix.Component
    import CypridinaWeb.CoreComponents

    @impl Backpex.Filter
    def label, do: "用户名"

    @impl Backpex.Filter
    def render_form(assigns) do
      ~H"""
      <.input field={@form[:value]} type="text" placeholder="输入用户名" />
      """
    end

    @impl Backpex.Filter
    def filter(query, %{"value" => value}) when value != "" do
      Ash.Query.filter(query, expr(contains(username, ^value)))
    end

    def filter(query, _params), do: query
  end

  # IP地址过滤器
  defmodule IpAddressFilter do
    use Backpex.Filter
    import Ash.Expr
    import Phoenix.Component
    import CypridinaWeb.CoreComponents

    @impl Backpex.Filter
    def label, do: "IP地址"

    @impl Backpex.Filter
    def render_form(assigns) do
      ~H"""
      <.input field={@form[:value]} type="text" placeholder="输入IP地址" />
      """
    end

    @impl Backpex.Filter
    def filter(query, %{"value" => value}) when value != "" do
      Ash.Query.filter(query, expr(contains(ip_address, ^value)))
    end

    def filter(query, _params), do: query
  end

  # 导出操作
  defmodule ExportAction do
    use Backpex.ItemAction
    import Phoenix.Component

    @impl Backpex.ItemAction
    def label(_assigns, _item), do: "导出记录"

    @impl Backpex.ItemAction
    def icon(assigns, _item) do
      ~H"""
      <Backpex.HTML.CoreComponents.icon
        name="hero-arrow-down-tray"
        class="h-5 w-5 cursor-pointer transition duration-75 hover:scale-110 hover:text-blue-600"
      />
      """
    end

    @impl Backpex.ItemAction
    def confirm(_assigns), do: "确认导出选中的记录吗？"

    @impl Backpex.ItemAction
    def fields, do: []

    def can?(_assigns, _item) do
      true
    end

    @impl Backpex.ItemAction
    def handle(socket, records, _params) do
      count = length(records)
      {:noreply, socket |> Phoenix.LiveView.put_flash(:info, "导出 #{count} 条领取记录")}
    end
  end

  # 统计分析操作
  defmodule StatisticsAction do
    use Backpex.ItemAction
    import Phoenix.Component

    @impl Backpex.ItemAction
    def label(_assigns, _item), do: "统计分析"

    @impl Backpex.ItemAction
    def icon(assigns, _item) do
      ~H"""
      <Backpex.HTML.CoreComponents.icon
        name="hero-chart-bar"
        class="h-5 w-5 cursor-pointer transition duration-75 hover:scale-110 hover:text-green-600"
      />
      """
    end

    @impl Backpex.ItemAction
    def confirm(_assigns), do: "确认对选中记录进行统计分析吗？"

    @impl Backpex.ItemAction
    def fields, do: []

    def can?(_assigns, _item) do
      true
    end

    @impl Backpex.ItemAction
    def handle(socket, records, _params) do
      # 统计分析逻辑
      total_records = length(records)
      unique_users = records |> Enum.map(& &1.user_id) |> Enum.uniq() |> length()
      unique_cdkeys = records |> Enum.map(& &1.cdkey_id) |> Enum.uniq() |> length()

      message = "统计结果: 总记录#{total_records}条, 涉及#{unique_users}个用户, #{unique_cdkeys}个CDKEY"
      {:noreply, socket |> Phoenix.LiveView.put_flash(:info, message)}
    end
  end

  @impl Backpex.LiveResource
  def mount(_params, _session, socket) do
    socket = assign(socket, :fluid?, true)
    {:ok, socket}
  end
end
