defmodule Teen.Live.ActivitySystem.WeeklyCardLive do
  @moduledoc """
  周卡活动管理页面
  """

  use AshBackpex.LiveResource

  import Phoenix.Component
  import Phoenix.LiveView

  backpex do
    resource Teen.ActivitySystem.WeeklyCard
    layout({Teen.Layouts, :admin})

    fields do
      field :id do
        module Backpex.Fields.Text
        label("ID")
        readonly(true)
        only([:show])
      end

      field :card_name do
        module Backpex.Fields.Text
        label("卡片名称")
      end

      field :card_type do
        module Backpex.Fields.Select
        label("卡片类型")

        options([
          {"周卡", "weekly"},
          {"月卡", "monthly"},
          {"季卡", "quarterly"}
        ])
      end

      field :price do
        module Backpex.Fields.Number
        label("价格")
      end

      field :daily_reward do
        module Backpex.Fields.Number
        label("每日奖励")
      end

      field :total_days do
        module Backpex.Fields.Number
        label("总天数")
      end

      field :bonus_reward do
        module Backpex.Fields.Number
        label("额外奖励")
      end

      field :is_active do
        module Backpex.Fields.Boolean
        label("是否激活")
      end

      field :inserted_at do
        module Backpex.Fields.DateTime
        label("创建时间")
        readonly(true)
      end

      field :updated_at do
        module Backpex.Fields.DateTime
        label("更新时间")
        readonly(true)
      end
    end
  end

  @impl Backpex.LiveResource
  def singular_name, do: "周卡活动"

  @impl Backpex.LiveResource
  def plural_name, do: "周卡活动"

  @impl Backpex.LiveResource
  def mount(_params, _session, socket) do
    socket = assign(socket, :fluid?, true)
    {:ok, socket}
  end
end
