defmodule Teen.Live.ActivitySystem.ScratchCardActivityLive do
  @moduledoc """
  刮刮卡活动管理页面
  """

  use AshBackpex.LiveResource

  import Phoenix.Component
  import Phoenix.LiveView

  backpex do
    resource Teen.ActivitySystem.ScratchCardActivity
    layout({Teen.Layouts, :admin})

    fields do
      field :id do
        module Backpex.Fields.Text
        label("ID")
        readonly(true)
        only([:show])
      end

      field :card_name do
        module Backpex.Fields.Text
        label("卡片名称")
      end

      field :card_type do
        module Backpex.Fields.Select
        label("卡片类型")

        options([
          {"选项1", "option1"},
          {"选项2", "option2"}
        ])
      end

      field :cost_amount do
        module Backpex.Fields.Number
        label("购买费用")
      end

      field :min_reward do
        module Backpex.Fields.Number
        label("最小奖励")
      end

      field :max_reward do
        module Backpex.Fields.Number
        label("最大奖励")
      end

      field :win_probability do
        module Backpex.Fields.Number
        label("中奖概率(%)")
      end

      field :daily_limit do
        module Backpex.Fields.Number
        label("每日限制次数")
      end

      field :is_active do
        module Backpex.Fields.Boolean
        label("是否激活")
      end

      field :inserted_at do
        module Backpex.Fields.DateTime
        label("创建时间")
        readonly(true)
      end

      field :updated_at do
        module Backpex.Fields.DateTime
        label("更新时间")
        readonly(true)
      end
    end
  end

  @impl Backpex.LiveResource
  def singular_name, do: "刮刮卡活动"

  @impl Backpex.LiveResource
  def plural_name, do: "刮刮卡活动"

  @impl Backpex.LiveResource
  def can?(assigns, action, item) do
    true
  end
end
